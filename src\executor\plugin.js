import * as t from '@babel/types'
import { parse } from '@babel/parser'
import { generate } from '@babel/generator'
import traverse from '@babel/traverse'

const STEP_IDENTIFIER = 'step'
const INVOKE_IDENTIFIER = 'invoke'
const GET_CTX_IDENTIFIER = 'getCtx'
const CTX_KEY = 'ctx'
const CODE_KEY = 'code'
const BLOCK_KEY = 'block'
const DATA_KEY = 'data'
const KEY_REGEXP = /^\s*([a-zA-Z]+):\s*/
const BLOCK_REGEXP = /block:\s*['"]([^'"]*)['"]/

function prepareStepArg () {
  return t.assignmentPattern(
    t.identifier(STEP_IDENTIFIER),
    t.arrowFunctionExpression([], t.blockStatement([]))
  )
}

function prepareCodeProp (code) {
  return t.objectProperty(
    t.identifier(CODE_KEY),
    t.stringLiteral(code)
  )
}

function prepareGetCtxFunc (vars) {
  const ctxId = t.identifier('ctx')
  const getCtxBodyStatements = [
    t.variableDeclaration('const', [
      t.variableDeclarator(ctxId, t.objectExpression([]))
    ])
  ]

  vars.forEach(varName => {
    getCtxBodyStatements.push(
      t.tryStatement(
        t.blockStatement([
          t.expressionStatement(
            t.assignmentExpression(
              '=',
              t.memberExpression(ctxId, t.identifier(varName)),
              t.identifier(varName)
            )
          )
        ]),
        t.catchClause(
          t.identifier('e'),
          t.blockStatement([])
        )
      )
    )
  })

  getCtxBodyStatements.push(
    t.returnStatement(ctxId)
  )

  return t.functionDeclaration(
    t.identifier(GET_CTX_IDENTIFIER),
    [],
    t.blockStatement(getCtxBodyStatements)
  )
}

function parseStep (comments) {
  const lines = []
  comments.forEach(
    c => c.value.split('\n').forEach(
      r => lines.push(r.trim())
    )
  )
  const withKeys = lines.filter(line => line.match(KEY_REGEXP))
  if (!withKeys.length) {
    return null
  }
  let text = withKeys.join(',')
  text = `${STEP_IDENTIFIER}({${text}, ${CTX_KEY}: ${GET_CTX_IDENTIFIER}()})`
  const parsed = parse(text)
  return parsed.program.body
}

function parseBlockName (c) {
  const v = c.value.match(BLOCK_REGEXP)
  if (v) {
    return v[1]
  }
  return null
}

function visitComments (handler) {
  const replacedComments = new Set()

  return {
    enter (path) {
      const node = path.node
      const leading = node.leadingComments
      const trailing = node.trailingComments

      t.removeComments(path.node)

      if (leading?.length) {
        if (!leading.some(c => replacedComments.has(c))) {
          leading.forEach(c => replacedComments.add(c))
          handler(leading, true, path)
        }
      }

      if (trailing?.length) {
        if (!trailing.some(c => replacedComments.has(c))) {
          trailing.forEach(c => replacedComments.add(c))
          handler(trailing, false, path)
        }
      }
    }
  }
}

function getDataDefaults (dataProps) {
  return dataProps
    .filter(p => p.value.type === 'ObjectExpression')
    .reduce((acc, p) => {
      const defaultProp = p.value.properties.find(
        prop => prop.type === 'ObjectProperty' && prop.key.name === 'default'
      )
      if (defaultProp) {
        acc[p.key.name] = defaultProp.value.value
      }
      return acc
    }, {})
}

function addDefaultsToData (args, dataProps) {
  const defaults = getDataDefaults(dataProps)
  args.properties.forEach(property => {
    if (property.value.type === 'AssignmentPattern') {
      const name = property.key.name
      if (!(name in defaults)) {
        dataProps.find(p => p.key.name === name).value.properties.push(
          t.objectProperty(
            t.identifier('default'),
            createLiteral(property.value.right.value)
          )
        )
      }
    }
  })
}

function traverseInvoke (path, dataProps) {
  // Список всех переменных, указанных в `data`
  const vars = dataProps.map(p => p.key.name)
  // Добавляем дефолтные значения в `data`, если они прописаны в аргументах
  addDefaultsToData(path.node.params[0], dataProps)
  // Добавляем аргумент step
  path.node.params.push(prepareStepArg())
  // Внедряем функцию для получения значений локальных переменных
  path.node.body.body.unshift(prepareGetCtxFunc(vars))
  // Добавляем аргумент step в вызовы invoke
  path.traverse({
    CallExpression (innerPath) {
      if (innerPath.node.callee.property?.name === INVOKE_IDENTIFIER) {
        innerPath.node.arguments.push(t.identifier(STEP_IDENTIFIER))
      }
    }
  })
  // Преобразуем комментарии в вызовы step
  path.traverse({
    ...visitComments((comments, before, path) => {
      const parsed = parseStep(comments)
      if (parsed) {
        if (before) {
          path.insertBefore(parsed)
        } else {
          path.insertAfter(parsed)
        }
      }
    })
  })
}

function compileInvokeCode (path) {
  const cloned = t.cloneDeepWithoutLoc(path.node)
  traverse.default(cloned, {
    ...visitComments((comments, before, path) => {
      const c = comments.find(c => c.value.includes(`${BLOCK_KEY}:`))
      if (c) {
        const blockName = parseBlockName(c)
        if (blockName) {
          path.addComment(before ? 'leading' : 'trailing', ` ${blockName} `)
        }
      }
    })
  }, path.scope, path.state, path.parentPath)
  return 'function ' + generate(cloned).code
}

function addCodeProp (path, invokeProp) {
  path.node.properties.push(prepareCodeProp(compileInvokeCode(invokeProp)))
}

function createLiteral (value) {
  switch (typeof value) {
    case 'number':
      return t.numericLiteral(value)
    case 'string':
      return t.stringLiteral(value)
    case 'boolean':
      return t.booleanLiteral(value)
    case 'object':
      if (value === null) {
        return t.nullLiteral()
      }
      throw new Error("Не поддерживаемый тип объекта")
    default:
      throw new Error("Неизвестный тип значения")
  }
}

function addDefaultsToInvoke (path, dataProps) {
  const defaults = getDataDefaults(dataProps)
  path.node.params[0].properties.forEach(property => {
    if (property.value.type === 'AssignmentPattern') {
      return
    }
    const key = property.key.name
    if (key in defaults) {
      property.value = t.assignmentPattern(property.key, createLiteral(defaults[key]))
    }
  })
}

function processModule (ast) {
  traverse.default(ast, {
    ObjectExpression (path) {
      const properties = path.get('properties')
      const invokeProp = properties.find(
        prop => prop.node.key.name === INVOKE_IDENTIFIER
      )

      if (invokeProp) {
        const dataProp = properties.find(
          prop => prop.node.key.name === DATA_KEY
        )
        const dataProps = dataProp.node.value.properties
          .filter(p => p.type === 'ObjectProperty')

        // Добавляем дефолтные значения в `invoke`
        addDefaultsToInvoke(invokeProp, dataProps)
        // Добавляем свойство `code` с исходным кодом в читабельном виде
        addCodeProp(path, invokeProp)
        // Преобразуем `invoke` для поддержки пошагового выполнения
        traverseInvoke(invokeProp, dataProps)
      } else {
        path.stop()
      }
    }
  })
}

export default function compileAlgorithmsPlugin () {
  return {
    name: 'compile-algorithms',
    enforce: 'pre',

    transform(code, id) {
      if (!id.includes('/algorithms/') || !code.includes(INVOKE_IDENTIFIER)) {
        return null
      }
      if (id.match(/.*test\.js/)) {
        return null
      }

      const ast = parse(code, { sourceType: 'module' })
      processModule(ast)
      const output = generate(ast, {}, code)

      return {
        code: output.code,
        map: output.map,
      }
    }
  }
}
