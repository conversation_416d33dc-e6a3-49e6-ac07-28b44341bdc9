import { z } from 'zod'

import { trimBraces, splitTopLevel } from '../utils.js'

export function toPairSchema (type, { toSchema }) {
  const keyType = type.items?.length ? toSchema(type.items[0]) : z.any()
  const valueType = type.items?.length > 1 ? toSchema(type.items[1]) : z.any()
  return z.object({ key: keyType, value: valueType })
}

export function createPair (type, { create }) {
  const keyType = type.items?.length ? type.items[0] : 'string'
  const valueType = type.items?.length > 1 ? type.items[1] : 'number'
  return {
    key: create(keyType),
    value: create(valueType)
  }
}

// Формат: "(ключ, значение)"
export function parsePair (str, type, { parse }) {
  str = trimBraces(str)
  const parts = splitTopLevel(str)
  if (parts.length !== 2) {
    throw new Error(`Неверный формат пары ${str}`)
  }
  const [key, value] = parts
  return {
    key: parse(key, type.items?.[0]),
    value: parse(value, type.items?.[1])
  }
}