<template>
  <Table>
    <TableCaption>Значения переменных</TableCaption>

    <TableHeader>
      <TableRow>
        <TableHead>Описание</TableHead>
        <TableHead>Имя</TableHead>
        <TableHead>Значение</TableHead>
      </TableRow>
    </TableHeader>

    <TableBody>
      <template v-for="(v, k) in data">
        <TableRow v-if="ctx[k] !== undefined && !hide.includes(k)" :key="k">
          <TableCell>{{ v.label }}</TableCell>
          <TableCell>{{ k }}</TableCell>
          <TableCell>{{ ctx[k] }}</TableCell>
        </TableRow>
      </template>
    </TableBody>
  </Table>
</template>

<script setup>
  import {
    Table,
    TableBody,
    TableHeader,
    TableHead,
    TableCell,
    TableRow,
    TableCaption
  } from '@/components/ui/table'

  defineProps({
    data: Object,
    ctx: Object,
    hide: {
      type: Array,
      default: () => []
    }
  })
</script>