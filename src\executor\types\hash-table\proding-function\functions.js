export function linearProbing(index, length, step = 1) {
    return (index + step) % length;
}

export function quadraticProbing(index, length, attempt) {
    return (index + attempt * attempt) % length;
}

export function doubleHashing(index, length, attempt, key) {
    return (index + attempt * hash(key, length)) % length;
}

export function hash(key, length) {
    let total = 0;
    let PRIME = 31;
    for (let i = 0; i < Math.min(key.length, 100); i++) {
      let char = key[i];
      let value = char.charCodeAt(0) - 96;
      total = (total * PRIME + value) % length;
    }
    return total;
}