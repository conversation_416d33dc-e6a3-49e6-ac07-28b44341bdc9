<template>
  <component is="style">{{ highlightStyles }}</component>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    current: String,
    highlightWidth: {
      type: String,
      default: '3px'
    },
    highlightColor: {
      type: String,
      default: 'var(--primary)'
    }
  })

  const highlightStyles = computed(
    () => `.mermaid-container g[id^="flowchart-${props.current}-"] polygon,
    .mermaid-container g[id^="flowchart-${props.current}-"] rect {
      stroke: ${props.highlightColor} !important;
      stroke-width: ${props.highlightWidth} !important;
      fill: #ddd !important;
    }
    .mermaid-container g[id^="flowchart-${props.current}-"] span {
      color: #000 !important;
    } 
    `
  )
</script>


