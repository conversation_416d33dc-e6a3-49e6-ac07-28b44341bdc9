<template>
  <Form
    :validation-schema="schema"
    :initial-values="initialValues"
    @submit="onSubmit"
    class="grid gap-4 w-full"
  >
    <AlgorithmField
      v-for="(config, field) in dataFields"
      :key="field"
      :name="field"
      :config="config"
    />

    <div class="flex justify-end">
      <Button type="submit">Подтвердить</Button>
    </div>
  </Form>
</template>

<script setup>
  import { computed } from 'vue'
  import { toTypedSchema } from '@vee-validate/zod'

  import { dataToSchema, create, getInputFields } from '@/executor'

  import { Form } from '@/components/ui/form'
  import { Button } from '@/components/ui/button'
  import AlgorithmField from './AlgorithmFormItem.vue'

  const props = defineProps({
    data: {
      type: Object,
      required: true
    },
    initial: {
      type: Object,
      default: () => {}
    },
    onlyInput: {
      type: Boolean,
      default: true
    },
    createRequired: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['submit'])

  const dataFields = computed(
    () => props.onlyInput ? getInputFields(props.data) : props.data
  )

  const initialValues = computed(() => {
    if (!props.createRequired) {
      return props.initial
    }
    const res = {}
    for (const k in dataFields.value) {
      res[k] = props.initial[k] ?? dataFields.value[k].default
      if (res[k] === undefined && dataFields.value[k].required) {
        res[k] = create(dataFields.value[k].type)
      }
    }
    return res
  })

  const schema = toTypedSchema(dataToSchema(dataFields.value))
  const onSubmit = (values) => {
    emit('submit', values)
  }
</script>