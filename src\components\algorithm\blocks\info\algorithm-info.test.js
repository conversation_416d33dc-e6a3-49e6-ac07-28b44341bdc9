import { mount } from '@vue/test-utils'

import AlgorithmInfo from './AlgorithmInfo.vue'
import { expect } from 'vitest'

test('renders correctly', () => {
  const wrapper = mount(AlgorithmInfo, {
    props: {
      algorithm: {
        label: 'Test',
        description: 'Test description',
        complexity: {
          time: 'O(n)',
          memory: 'O(1)'
        }
      }
    }
  })

  expect(wrapper.text()).toContain('Test')
  expect(wrapper.text()).toContain('Test description')
  expect(wrapper.text()).toContain('O(n)')
  expect(wrapper.text()).toContain('O(1)')
  expect(wrapper.text()).not.toContain('Худший случай')
  expect(wrapper.text()).not.toContain('Средний случай')
  expect(wrapper.text()).not.toContain('Лучший случай')
})