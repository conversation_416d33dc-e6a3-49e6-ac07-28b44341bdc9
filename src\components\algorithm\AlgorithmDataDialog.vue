<template>
  <Dialog v-model:open="open">
    <DialogTrigger>
      <slot />
    </DialogTrigger>

    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>Перезапустить алгоритм</DialogTitle>
        <DialogDescription>
          Укажите входные данные, с которыми нужно перезапустить алгоритм.
        </DialogDescription>
      </DialogHeader>

      <AlgorithmForm
        :data="data"
        :initial="initial"
        create-required
        @submit="onSubmit"
      />
    </DialogContent>
  </Dialog>
</template>

<script setup>
  import { ref, watch } from 'vue'

  import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
  } from '@/components/ui/dialog'
  import { AlgorithmForm } from './form'

  defineProps({
    data: Object,
    initial: Object
  })
  const emit = defineEmits(['submit'])
  const open = ref(false)

  function onSubmit (values) {
    emit('submit', values)
    open.value = false
  }
</script>