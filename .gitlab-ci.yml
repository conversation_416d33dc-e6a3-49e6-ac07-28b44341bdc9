image: node:latest

cache:
  paths:
    - node_modules/

stages:
  - test
  - build
  - deploy

unit-test-job:
  stage: test
  script:
    - npm install
    - npm run test

build-job:
  stage: build
  only:
    - prod
  script:
    - npm install --production
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 7 days

deploy-job:
  stage: deploy
  only:
    - prod
  script:
    - apt-get update -y && apt-get install -y lftp
    - lftp -c "
        open -u $FTP_USERNAME,$FTP_PASSWORD $FTP_HOST;
        mirror -R ./dist/ ./public_html/;
      "
