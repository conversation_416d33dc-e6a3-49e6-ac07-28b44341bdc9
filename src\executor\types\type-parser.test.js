import { expect, test } from 'vitest'

import { parseTypeString } from './type-parser'

test('parse type string', () => {
  expect(parseTypeString('array')).toEqual({ name: 'array' })
  expect(parseTypeString('array<number>')).toEqual({ name: 'array', items: [{ name: 'number' }] })
  expect(parseTypeString('array<number>.sorted')).toEqual({ name: 'array', items: [{ name: 'number' }], attrs: ['sorted'] })
  expect(parseTypeString('array<array<number>.sorted>')).toEqual({ name: 'array', items: [{ name: 'array', items: [{ name: 'number' }], attrs: ['sorted'] }] })
  expect(parseTypeString('graph<char>(weighted<integer>,directed.acyclic)')).toEqual({
    name: 'graph',
    items: [{ name: 'char' }],
    sub: [
      { name: 'weighted', items: [{ name: 'integer' }] },
      { name: 'directed', attrs: ['acyclic'] }
    ]
  })
  expect(parseTypeString('bst<number,hash<string,string>(chaining)>')).toEqual({
    name: 'bst',
    items: [
      { name: 'number' },
      {
        name: 'hash',
        items: [{ name: 'string' }, { name: 'string' }],
        sub: [{ name: 'chaining' }]
      }
    ]
  })
})
