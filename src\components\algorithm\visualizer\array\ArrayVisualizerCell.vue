<template>
  <TableCell
    :style="cellStyle(cell, pos)"
    :class="[
      ...cellClass(cell, pos),
      ...(isHighlighted ? highlightClass(cell, pos) : []),
      'text-center',
      'border-1',
      'border-solid'
    ]"
  >
    {{ cellContent(cell, pos) }}
  </TableCell>
</template>

<script setup>
  import { computed } from 'vue'

  import { TableCell } from '@/components/ui/table'
  import { useCellProps, useCell } from './cell'

  const props = defineProps({
    cell: [Object, Number, String],
    pos: [Array, Number],
    ...useCellProps
  })
  const { isHighlighted } = useCell(props)
</script>
