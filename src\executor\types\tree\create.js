import { BinarySearchTree } from './bst.js'
import { bstInsert } from '../../algorithms/bst/index.js'

export function createBst (type, { create }) {
  const keyType = type.items?.length ? type.items[0] : 'any'
  const valueType = type.items?.[1]
  const tree = new BinarySearchTree()
  const nodesCount = 10
  const insert = bstInsert(keyType.name, valueType ? valueType.name : 'any')

  for (let i = 0; i < nodesCount; i++) {
    insert.invoke({
      tree,
      key: create(keyType),
      value: valueType ? create(valueType) : undefined
    })
  }
  return tree
}
