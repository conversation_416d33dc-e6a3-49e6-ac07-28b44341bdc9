<template>
  <div>
    <v-network-graph :nodes="nodes" :edges="edges" :configs="configs" v-model:zoom-level="zoomLevel">
      <template #edge-label="{ edge, ...slotProps }">
        <v-edge-label :text="`${props.edgeLabel(edge.label)}`" vertical-align="above" align="center"
          v-bind="slotProps" />
      </template>
    </v-network-graph>
  </div>
</template>

<script setup>
import { watch, ref, computed } from "vue"
import { VNetworkGraph, VEdgeLabel } from "v-network-graph"

import "v-network-graph/lib/style.css"
import { Graph } from "@/executor"
import { ForceLayout } from "v-network-graph/lib/force-layout";

const props = defineProps({
  graph: {
    type: Graph,
    required: false,
    default: () => {
      const graph = new Graph({ directed: true })
      const node1 = graph.addNode(11)
      const node2 = graph.addNode(22)
      const node3 = graph.addNode("Z")
      const node4 = graph.addNode(44)

      graph.addLink(node1, node2)
      graph.addLink(node2, node3)
      graph.addLink(node3, node4)
      return graph

    },
  },
  highlightNodes: Array,
  highlightEdges: Array,
  highlightNodeStyle: {
    type: Object,
    required: false,
    default: () => ({ color: "white", labelColor: "#121212" }),
  },
  highlightEdgeStyle: {
    type: Object,
    required: false,
    default: () => ({ color: "white", animate: true }),
  },
  nodeLabel: {
    type: Function,
    required: false,
    default: (data) => data || '',
  },
  edgeLabel: {
    type: Function,
    required: false,
    default: (data) => data || '',
  },
  nodeStyle: {
    /// функция ожидает на вход ЗНАЧЕНИЕ узла
    type: Function,
    required: false,
    default: (nodeData) => {
      return {
        color: "#121212",
      }

    }
  },
  edgeStyle: {
    /// функция ожидает на вход ЗНАЧЕНИЕ ребра
    type: Function,
    required: false,
    default: (edgeData) => {
      return { color: "#808080" }
    }
  },
});

const zoomLevel = ref(0.8)

const d3Forces = new ForceLayout({
  positionFixedByDrag: false,
  positionFixedByClickWithAltKey: true,
  createSimulation: (d3, nodes, edges) => {
    const forceLink = d3.forceLink(edges).id(d => d.id)
    return d3
      .forceSimulation(nodes)
      .force("edge", forceLink.distance(70).strength(0.3))
      .force("charge", d3.forceManyBody().strength(-400).theta(0.9))
      .force("center", d3.forceCenter().strength(0.05))
      .alphaMin(0.001)
  }
})

const configs = computed(() => {
  let result = {
    node: {
      normal: {
        color: (node) => node.color,
        radius: (20 * zoomLevel.value),
        width: 32,
        height: 32,
        strokeColor: "#808080",
        strokeWidth: 1,
      },
      hover: {
        color: (node) => node.color,
      },
      label: {
        text: (node) => props.nodeLabel(node.name),
        color: (node) => node.labelColor || "#808080",
        fontSize: 12,
        fontFamily: 'Arial',
        direction: 'center', // или 'north', 'south' и т.д.
        margin: 4,
      }
    },
    edge: {
      normal: {
        color: (edge) => (edge.color),
        width: edge => (edge.animate ? 4 : 2) * zoomLevel.value,
        dasharray: edge => (edge.animate ? "4" : "0"),
        animate: edge => !!edge.animate,
      },
      hover: {
        color: (edge) => edge.color,
      },
      label: {
        visible: true,
        fontSize: 10,
        color: "white",
        background: {
          visible: false,
        },
      },
    },
    view: {
      layoutHandler: d3Forces,
      scalingObjects: true,
      minZoomLevel: 0.5,
      maxZoomLevel: 1.3,
      layout: {
        type: 'force',
        auto: true,
        fit: true, //отвечает за автоподгонку под размер контейнера
      },
    },
  }

  if (props.graph.isDirected) {
    result.edge = {
      ...result.edge,
      marker: {
        target: {
          type: "arrow",
          width: 6,
          height: 6,
          margin: -4,
          color: "#808080",
        },
      },
    };
  }
  return result
})

/* блок стилей css для node и edge
node:
radius - размер
strokeColor - цвет обводки
strokeWidth - толщина обводки
*/
const edges = computed(() => {
  let result = {}
  let countOfEdges = 0
  const graph = props.graph
  const temp = graph.nodes
  const uniqueEdges = new Map()
  for (const node of temp) {
    const links = graph.getLinks(node)
    for (const link of links) {
      const edgeKey1 = `${node.id}-${link.id}`
      const edgeKey2 = `${link.id}-${node.id}`
      if (!(uniqueEdges.has(edgeKey1) || uniqueEdges.has(edgeKey2))) {
        result[`edge${++countOfEdges}`] = {
          source: `node${node.id}`,
          target: `node${link.id}`,
          label: link.data,
          ...props.edgeStyle(link.data),
        };
        uniqueEdges.set(edgeKey1, countOfEdges)
        uniqueEdges.set(edgeKey2, countOfEdges)
      }
    }
  }
  //подсветка связей
  if (props.highlightEdges) {
    for (const pair of props.highlightEdges) {
      const key = `${pair[0]}-${pair[1]}`
      const idx = `edge${uniqueEdges.get(key)}`
      result[idx] = { ...result[idx], ...props.highlightEdgeStyle }
    }
  }

  return result
})
const nodes = computed(() => {
  let result = {}
  const graph = props.graph
  const temp = graph.nodes
  for (const node of temp) {
    //устанавливаем значение узлов в формате библиотечки
    result[`node${node.id}`] = { name: node.data, ...props.nodeStyle(node.data) }
  }

  //подсветка узлов
  if (props.highlightNodes) {
    for (const idx of props.highlightNodes) {
      result[`node${idx}`] = {
        ...result[`node${idx}`],
        ...props.highlightNodeStyle,
      };
    }
  }

  return result
})


</script>

<style scoped>
div {
  border-width: 1px;
  border-color: oklch(lightness chroma hue);
  border-style: solid;
  height: 400px;
}
</style>
