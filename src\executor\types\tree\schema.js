import { z } from 'zod'

import { instanceSchema } from '../utils'
import { BinarySearchTree, AVLTree, RedBlackTree } from './bst'

function addNodesSchema (schemaParams, type, toSchema) {
  if (type.items?.length) {
    const nodeKeyType = type.items[0]
    const nodeValueType = type.items?.length > 1 ? type.items[1] : 'any'
    schemaParams.nodes = z.array(
      z.object({
        key: toSchema(nodeKeyType),
        value: toSchema(nodeValueType)
      }).passthrough()
    )
  }
  return schemaParams
}

export function toBstSchema (type, { toSchema }) {
  return instanceSchema(BinarySearchTree, addNodesSchema({}, type, toSchema))
}

export function toAvlScheme (type, { toSchema }) {
  return instanceSchema(AVLTree, addNodesSchema({}, type, toSchema))
}

export function toRbtreeSchema (type, { toSchema }) {
  return instanceSchema(RedBlackTree, addNodesSchema({}, type, toSchema))
}