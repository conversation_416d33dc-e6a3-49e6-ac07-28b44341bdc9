<template>
    <div>
        <v-network-graph :nodes="nodes" :configs="configs" :edges="edges" :zoom-level="zoomLevel">
            <template #edge-label="{ edge, ...slotProps }">
                <v-edge-label :text="`${props.edgeLabel(edge.label)}`" vertical-align="above" align="center"
                    v-bind="slotProps" />
            </template>
        </v-network-graph>
    </div>
</template>

<script setup>
import { watch, ref, computed } from "vue"
import { VNetworkGraph, VEdgeLabel } from "v-network-graph"
import "v-network-graph/lib/style.css"
import { Tree } from "@/executor/types/tree/tree"
import { ForceLayout } from "v-network-graph/lib/force-layout";
import { boolean } from "zod";
import { height, label, style, target, width } from "happy-dom/lib/PropertySymbol.js";


const props = defineProps(
    {
        tree: {
            type: Tree,
            required: false,
            default: () => {
                const t = new Tree()

                const n1 = t.addNode({ key: 1 });

                // Уровень 2
                const n2 = t.addNode({ key: 2 }, n1);
                const n3 = t.addNode({ key: 3 }, n1);

                // Уровень 3
                const n4 = t.addNode({ key: 4 }, n2);
                const n5 = t.addNode({ key: 5 }, n2);
                const n6 = t.addNode({ key: 6 }, n3);
                const n7 = t.addNode({ key: 7 }, n3);

                // Уровень 4
                const n8 = t.addNode({ key: 8 }, n4);
                const n9 = t.addNode({ key: 9 }, n4);
                const n10 = t.addNode({ key: 10 }, n5);
                const n11 = t.addNode({ key: 11 }, n5);
                const n12 = t.addNode({ key: 12 }, n6);
                const n13 = t.addNode({ key: 13 }, n6);
                const n14 = t.addNode({ key: 14 }, n7);
                const n15 = t.addNode({ key: 15 }, n7);

                // Уровень 5
                const n16 = t.addNode({ key: 16 }, n8);
                const n17 = t.addNode({ key: 17 }, n8);
                const n18 = t.addNode({ key: 18 }, n9);
                const n19 = t.addNode({ key: 19 }, n9);
                const n20 = t.addNode({ key: 20 }, n10);
                const n21 = t.addNode({ key: 21 }, n10);
                const n22 = t.addNode({ key: 22 }, n11);
                const n23 = t.addNode({ key: 23 }, n11);
                const n24 = t.addNode({ key: 24 }, n12);
                const n25 = t.addNode({ key: 25 }, n12);
                const n26 = t.addNode({ key: 26 }, n13);
                const n27 = t.addNode({ key: 27 }, n13);
                const n28 = t.addNode({ key: 28 }, n14);
                const n29 = t.addNode({ key: 29 }, n14);
                const n30 = t.addNode({ key: 30 }, n15);

                return t;
            }
        },
        highlightNodes: Array,
        highlightEdges: Array,
        highlightNodeStyle: {
            type: Object,
            required: false,
            default: () => ({ color: "white", labelColor: "#121212" }),
        },
        highlightEdgeStyle: {
            type: Object,
            required: false,
            default: () => ({ color: "white", animate: true }),
        },
        nodeLabel: {
            type: Function,
            required: false,
            default: (data) => data || '',
        }, edgeLabel: {
            type: Function,
            required: false,
            default: (data) => data || '',
        },
        nodeStyle: {
            /// функция ожидает на вход ЗНАЧЕНИЕ узла
            type: Function,
            required: false,
            default: (nodeData) => {
                return {
                    color: "#121212",
                    width: 32,
                    height: 32,
                    type: "rect",
                    borderRadius: 8,
                    strokeColor: "#808080",
                    strokeWidth: 1,

                    labelFontSize: 12,
                    labelFontFamily: 'Arial',
                    labelDirection: 'center',
                    labelMargin: 4,

                }
            }
        },
        edgeStyle: {
            /// функция ожидает на вход ЗНАЧЕНИЕ ребра
            type: Function,
            required: false,
            default: (edgeData) => {
                return {
                    color: "#808080",
                    width: (edge) => (edge.animate ? 4 : 2),
                    dasharray: (edge) => (edge.animate ? "4" : "0"),
                    labelFontSize: 10,
                    labelColor: "white",


                }
            }
        },
        isVertical: {
            type: Boolean,
            required: false,
            default: false,
        },
    }
)

const zoomLevel = ref(0.7)

const d3Forces = new ForceLayout({
    positionFixedByDrag: false,
    positionFixedByClickWithAltKey: true,
    createSimulation: (d3, nodes, edges) => {
        let targetX = undefined
        let targetY = undefined
        let deltaX = undefined
        let deltaY = undefined
        let q = undefined
        let n = undefined
        if (props.isVertical) {
            nodes[0].fx = 0
            nodes[0].fy = -175
            nodes[0].fixed = true
            targetX = 0
            targetY = 10000
            deltaX = 0
            deltaY = -50
            q = -5;
            n = -nodes.length
        }
        else {
            nodes[0].fx = -800
            nodes[0].fy = 0
            nodes[0].fixed = true
            targetX = 10000
            targetY = 0
            deltaX = -50
            deltaY = 0
            q = 1
            n = nodes.length
        }

        const forceLink = d3.forceLink(edges).id(d => d.id)
        const fixedX = d3.forceX().x(d => d.fx || targetX).strength(d => d.fixed ? 1 : 0.003)
        const fixedY = d3.forceY().y(d => d.fy || targetY).strength(d => d.fixed ? 1 : 0.003)

        return d3
            .forceSimulation(nodes)
            .force("edge", forceLink.distance(70 + n).strength(0.9))
            .force("charge", d3.forceManyBody().strength(-300 - q * n).theta(0.1))
            .force("x", fixedX)
            .force("y", fixedY)
            .force("collide", d3.forceCollide().radius(20).strength(0.05))
            .force("additional", d3.forceCenter(nodes[0].fx + deltaX, nodes[0].fy + deltaY).strength(-0.007))
            .force("additional2", d3.forceCenter(nodes[0].fx + deltaX, nodes[0].fy + deltaY).strength(0.007))
            .alphaMin(0.01)
    }
})

const configs = computed(() => {
    let result = {
        node: {
            normal: {
                color: (node) => node.color,
                type: (node) => node.type,
                radius: (node) => node.radius,
                width: (node) => node.width,
                height: (node) => node.height,
                borderRadius: (node) => node.borderRadius,
                strokeColor: (node) => node.strokeColor,
                strokeWidth: (node) => node.strokeWidth,
            },
            hover: {
                color: (node) => node.color,
            },
            label: {
                text: (node) => props.nodeLabel(node.name),
                color: (node) => node.labelColor || "#808080",
                fontSize: (node) => node.labelFontSize,
                fontFamily: (node) => node.labelFontFamily,
                direction: (node) => node.labelDirection,
                margin: (node) => node.labelMargin,
            }
        },
        edge: {
            normal: {
                color: (edge) => (edge.color),
                width: (edge) => edge.width(edge),
                dasharray: (edge) => edge.dasharray(edge),
                animate: (edge) => !!edge.animate,
            },
            hover: {
                color: (edge) => edge.color,
            },
            label: {
                visible: true,
                color: (edge) => edge.labelColor,
                fontSize: (edge) => label.fontSize,
                background: {
                    visible: false,
                },
            },
            marker: {
                target: {
                    type: "arrow",
                    width: 6,
                    height: (edge) => 6,
                    color: (edge) => "white",
                    margin: (edge) => -4,
                }
            },
        },
        view: {
            layoutHandler: d3Forces,
            scalingObjects: true,
            minZoomLevel: 0.5,
            maxZoomLevel: 1.3,
            layout: {
                type: 'force',
                auto: true,
                fit: true, //отвечает за автоподгонку под размер контейнера
            },
        },
    }
    return result
})

const nodes = computed(() => {
    const tree = props.tree
    let result = {}
    const all = tree.nodes;
    const root = tree.root

    for (const node of all) {
        result[`node${node.id}`] = { name: node.key, ...props.nodeStyle(node.key) }
    }
    result[`node${root.id}`] = { name: root.key, ...props.nodeStyle(root.key), x: 10000, y: 200 }
    if (props.highlightNodes) {
        for (const idx of props.highlightNodes) {
            result[`node${idx}`] = {
                ...result[`node${idx}`],
                ...props.highlightNodeStyle,
            };
        }
    }
    return result;
})
const edges = computed(() => {
    let result = {}
    const uniqueEdges = new Map()
    let countOfEdges = 0
    const tree = props.tree

    const all = tree.nodes;

    for (const node of all) {
        const children = tree.getChildNodes(node)
        if (children) {
            for (const child of children) {
                const edgeKey1 = `${node.id}-${child.id}`
                const edgeKey2 = `${child.id}-${node.id}`
                if (!(uniqueEdges.has(edgeKey1) || uniqueEdges.has(edgeKey2))) {
                    result[`edge${++countOfEdges}`] = {
                        source: `node${node.id}`,
                        target: `node${child.id}`,
                        ...props.edgeStyle()
                    };
                    uniqueEdges.set(edgeKey1, countOfEdges)
                    uniqueEdges.set(edgeKey2, countOfEdges)
                }

            }
        }

    }
    if (props.highlightEdges) {
        for (const pair of props.highlightEdges) {
            const key = `${pair[0]}-${pair[1]}`
            const idx = `edge${uniqueEdges.get(key)}`
            result[idx] = { ...result[idx], ...props.highlightEdgeStyle }
        }
    }
    return result;
})

</script>

<style scoped>
div {
    border-width: 1px;
    border-color: oklch(lightness chroma hue);
    border-style: solid;
    height: 400px;
}
</style>