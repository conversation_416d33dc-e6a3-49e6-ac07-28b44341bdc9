import { expect, test } from 'vitest'

import { create } from './create'
import { arraySorted } from './array'

test('creates types', () => {
  expect(() => create('unknown')).toThrowError()
  expect(() => create('string')).not.toThrowError()
  expect(() => create('char')).not.toThrowError()
  expect(() => create('number')).not.toThrowError()
  expect(() => create('integer')).not.toThrowError()
  expect(() => create('array')).not.toThrowError()
  expect(() => create('graph')).not.toThrowError()
  expect(() => create('boolean')).not.toThrowError()
  expect(() => create('any')).not.toThrowError()
  expect(() => create('array<number>')).not.toThrowError()
})

test('creates weighted directed graph', () => {
  const graph = create('graph<char>(weighted<number>,directed)')
  expect(graph.isDirected).toBe(true)
  expect(graph.nodes.length).toBeGreaterThan(1)
  expect(graph.links.length).toBeGreaterThan(1)
  expect(graph.links[0].data).toBeTypeOf('number')
  expect(graph.nodes[0].data).toBeTypeOf('string')
  
  const link = graph.links[0]
  const link2 = graph.getReversedLink(link)
  expect(link.data === link2?.data).toBe(false)
})

test('creates weighted graph', () => {
  const graph = create('graph<char>(weighted<number>)')
  const link1 = graph.links[0]
  const link2 = graph.getReversedLink(link1)
  expect(link1.data === link2.data).toBe(true)
})

test('creates bst', () => {
  const tree = create('bst<number,string>')
  expect(tree.root).not.toBeNull()
  expect(tree.root.key).toBeTypeOf('number')
  expect(tree.root.value).toBeTypeOf('string')
  expect(tree.nodes.length).toBeGreaterThan(1)
})

test('creates sorted array', () => {
  const array = create('array<number>.sorted')
  expect(array.length).toBeGreaterThan(1)
  expect(array[0]).toBeTypeOf('number')
  expect(arraySorted(array)).toBe(true)
})

test('creates unique array', () => {
  const array = create('array<number>.unique')
  expect(array.length).toBeGreaterThan(1)
  expect(array[0]).toBeTypeOf('number')
  expect(array.length).toBe(new Set(array).size)
})

test('creates sorted string array', () => {
  const array = create('array<string>.sorted')
  expect(array.length).toBeGreaterThan(1)
  expect(array[0]).toBeTypeOf('string')
  expect(arraySorted(array)).toBe(true)
})
