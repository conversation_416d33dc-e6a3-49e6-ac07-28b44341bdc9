import { expect, test } from 'vitest'

import { toSchema, dataToSchema, validateByType } from './schema'
import { Graph } from './graph'
import { BinarySearchTree, AVLTree } from './tree'
import algorithms from '../algorithms'
import { HashTableChained , HashTableOpenAddressing} from './hash-table'

test('to schema', () => {
  expect(() => toSchema('unknown')).toThrowError()
  expect(() => toSchema('string')).not.toThrowError()
  expect(() => toSchema('char')).not.toThrowError()
  expect(() => toSchema('number')).not.toThrowError()
  expect(() => toSchema('integer')).not.toThrowError()
  expect(() => toSchema('array')).not.toThrowError()
  expect(() => toSchema('graph')).not.toThrowError()
  expect(() => toSchema('boolean')).not.toThrowError()
  expect(() => toSchema('any')).not.toThrowError()
  expect(() => toSchema('array<number>')).not.toThrowError()
  expect(() => toSchema('graph<char>(weighted<number>,directed)')).not.toThrowError()
  expect(() => toSchema('hash<string,string>')).not.toThrowError()
  
  expect(
    () => validateByType(
      new Graph({ directed: true }),
      'graph<char>(weighted<number>,directed)'
    )
  ).not.toThrowError()

  expect(
   () => validateByType(
    new HashTableChained(),
    'hash<string,string>'
   )
  ).not.toThrowError()
})

test('to array schema', () => {
  expect(() => validateByType([1,2,3], 'array<number>')).not.toThrowError()
  expect(() => validateByType([1,2,'3'], 'array<number>')).toThrowError()
  expect(() => validateByType([1,2,3.5], 'array<number>')).not.toThrowError()
  expect(() => validateByType([1,2,3.5], 'array<integer>')).toThrowError()
})

test('to graph schema', () => {
  const graph = new Graph({ directed: true })
  const n1 = graph.addNode('a')
  const n2 = graph.addNode('b')
  const n3 = graph.addNode('c')
  graph.addLink(n1, n2, 1)
  graph.addLink(n2, n3, 2.5)
  
  expect(
    () => toSchema('graph').parse({ type: 'graph', nodes: [], links: [] })
  ).toThrowError()
  const validatedGraph = toSchema('graph<char>(weighted<number>,directed)').parse(graph)
  expect(validatedGraph).toBe(graph)
  expect(validatedGraph instanceof Graph).toBe(true)
  expect(
    () => validateByType(graph, 'graph<char>(weighted<number>,directed)')
  ).not.toThrowError()
  expect(
    () => validateByType(graph, {
      name: 'graph',
      items: [{ name: 'char' }],
      sub: ['weighted<number>', 'directed']
    })
  ).not.toThrowError()
})

test('to bst schema', () => {
  const tree = new BinarySearchTree()
  const n1 = tree.addNode({ key: 1 })
  const n2 = tree.addNode({ key: 2 }, n1)
  const n3 = tree.addNode({ key: 3 }, n1)
  const n4 = tree.addNode({ key: 4 }, n2)
  const n5 = tree.addNode({ key: 5 }, n2)

  const avl = new AVLTree()
  avl.addNode({ key: 1 })

  expect(() => validateByType(tree, 'bst<number>')).not.toThrowError()
  expect(() => validateByType(avl, 'bst<number>')).not.toThrowError()
  expect(() => validateByType(avl, 'avl<number>')).not.toThrowError()
  expect(() => validateByType(tree, 'avl<number>')).toThrowError()
  expect(() => validateByType(tree, 'bst<char>')).toThrowError()

  expect(
    () => validateByType(tree, {
      name: 'bst',
      items: [{ name: 'number' }]
    })
  ).not.toThrowError()
})

test('to hash table schema', () => {
  const chainedHash = new HashTableChained()
  const openAddressingHash = new HashTableOpenAddressing()
  expect(() => validateByType(chainedHash, 'hash<string,string>')).not.toThrowError()
  expect(() => validateByType(openAddressingHash, 'hash<string,string>')).not.toThrowError()
})

test('creates schema for all algorithms', () => {
  Object.values(algorithms).forEach(algorithm => {
    expect(() => dataToSchema(algorithm.data)).not.toThrowError()
  })
})
