<template>
  <Sidebar v-bind="props">

    <SidebarContent>
      <SidebarGroup>
        <AlgorithmSearch class="mt-5" />
      </SidebarGroup>

      <template v-for="group in nav">
        <NavGroupCategorized
          v-if="group.categorized"
          :items="group.items"
          :title="group.title"
        />
        <NavGroup
          v-else
          :items="group.items"
        />
      </template>
    </SidebarContent>

    <SidebarRail />
  </Sidebar>
</template>

<script setup>
  import {
    Sidebar,
    SidebarContent,
    SidebarRail,
    SidebarGroup
  } from '@/components/ui/sidebar'

  import AlgorithmSearch from './AlgorithmSearch.vue'
  import NavGroup from './NavGroup.vue'
  import NavGroupCategorized from './NavGroupCategorized.vue'

  import nav from '@/data/nav'

  const props = {
    collapsible: 'offcanvas',
  }
</script>
