import { parse } from './parse'
import { BinarySearchTree } from './tree'

test('parse string', () => {
  expect(parse('123', 'string')).toBe('123')
  expect(parse(123, 'string')).toBe('123')
  expect(parse(true, 'string')).toBe('true')
})

test('parse char', () => {
  expect(parse('123', 'char')).toBe('1')
  expect(parse(123, 'char')).toBe('1')
  expect(parse(true, 'char')).toBe('t')
})

test('parse number', () => {
  expect(parse('123', 'number')).toBe(123)
  expect(parse(123, 'number')).toBe(123)
  expect(parse(true, 'number')).toBe(1)
})

test('parse integer', () => {
  expect(parse('123', 'integer')).toBe(123)
  expect(parse(123.5, 'integer')).toBe(123)
  expect(parse(true, 'integer')).toBe(1)
})

test('parse pair', () => {
  expect(parse('(1,2)', 'pair')).toEqual({ key: '1', value: '2' })
  expect(parse('(1,2)', 'pair<number,number>')).toEqual({ key: 1, value: 2 })
})

test('parse array', () => {
  expect(parse('[1,2,3]', 'array')).toEqual(['1', '2', '3'])
  expect(parse('[1,2,3]', 'array<number>')).toEqual([1, 2, 3])
  expect(parse('[[1, 2], [3, 4], [5, 6]]', 'array<array<number>>')).toEqual([[1, 2], [3, 4], [5, 6]])
  expect(parse('[(1,2), (3,4), (5,6)]', 'array<pair<number,number>>')).toEqual([{ key: 1, value: 2 }, { key: 3, value: 4 }, { key: 5, value: 6 }])
})

test('parse bst', () => {
  const bst = parse('1,2,3', 'bst')
  expect(bst).toBeInstanceOf(BinarySearchTree)
  expect(bst.nodes.length).toBe(3)

  const bst2 = parse('1,2,3', 'bst<number>')
  expect(bst2).toBeInstanceOf(BinarySearchTree)
  expect(bst2.nodes.length).toBe(3)

  const bst3 = parse('[(1,vhj),(2,vjh),(3,vjv)]', 'bst<number,string>')
  expect(bst3).toBeInstanceOf(BinarySearchTree)
  expect(bst3.nodes.length).toBe(3)
  expect(bst3.nodes[0].value).toBe('vhj')
})
