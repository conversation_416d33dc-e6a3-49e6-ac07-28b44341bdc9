import { z } from 'zod'
import i18next from 'i18next'
import { zodI18nMap } from 'zod-i18n-map'
import translation from 'zod-i18n-map/locales/ru/zod.json'

import { parseTypeString } from './type-parser'
import { toStringSchema, toCharSchema } from './string'
import { toNumberSchema, toIntegerSchema } from './number'
import { toArraySchema } from './array'
import { toGraphSchema } from './graph'
import { toPairSchema } from './pair'
import { toBstSchema, toAvlScheme, toRbtreeSchema } from './tree'
import { toHashTableSchema } from './hash-table'

i18next.init({
  lng: 'ru',
  resources: {
    ru: { zod: translation },
  },
})
z.setErrorMap(zodI18nMap)

const converters = {
  string: toStringSchema,
  char: toCharSchema,
  number: toNumberSchema,
  integer: toIntegerSchema,
  array: toArraySchema,
  graph: toGraphSchema,
  pair: toPairSchema,
  bst: toBstSchema,
  avl: toAvlScheme,
  rbtree: toRbtreeSchema,
  hash: toHashTableSchema,
  boolean: () => z.boolean(),
  any: () => z.any()
}

/**
 * Преобразование описания типа в схему валидации
 * @param {String|Object} type 
 */
export function toSchema (type) {
  type = parseTypeString(type)
  const converter = converters[type.name]
  if (!converter) {
    throw new Error(`Неизвестный тип ${type.name}`)
  }
  return converter(type, { toSchema, parseType: parseTypeString })
}

/**
 * Преобразование спецификации данных в схему валидации
 * @param {Object} data 
 */
export function dataToSchema (data) {
  const objectSchema = {}
  for (const k in data) {
    const field = data[k]
    objectSchema[k] = toSchema(field.type)
    if (field.default !== undefined) {
      objectSchema[k] = objectSchema[k].default(field.default)
    }
    if (!field.required) {
      objectSchema[k] = objectSchema[k].nullish()
    }
  }
  return z.object(objectSchema)
}

/**
 * Валидация значения по схеме
 * @param {*} value 
 * @param {*} schema 
 */
export function validateBySchema (value, schema) {
  return schema.parse(value)
}

/**
 * Валидация значения по его спецификации
 * @param {*} value 
 * @param {String|Object} type 
 */
export function validateByType (value, type) {
  return toSchema(type).parse(value)
}

/**
 * Валидация целого контекста (объекта с переменными) по спецификации
 * @param {Object} ctx 
 * @param {Object} data 
 */
export function validateByData (ctx, data) {
  return dataToSchema(data).parse(ctx)
}

console.log(toSchema('array<number>'))
