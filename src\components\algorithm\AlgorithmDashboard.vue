<template>
  <ResizablePanelGroup direction="horizontal">
    <ResizablePanel
      :default-size="isMobile ? 100 : 66"
      :min-size="33"
      class="p-5"
    >
      <AlgorithmControls
        :executor="executor"
        :auto-executor="autoExecutor"
        :data="algorithm.data"
        :initial="executor.inputs"
        @restart="onRestart"
      />

      <div class="my-8">
        <template v-for="k in visualizable" :key="k">
          <Visualizer
            v-if="ctx[k] !== undefined"
            :type="algorithm.data[k].type"
            :value="ctx[k]"
          />
        </template>
      </div>

      <AlgorithmVariables
        :data="algorithm.data"
        :ctx="ctx"
        :hide="visualizable"
        class="mb-4"
      />
    </ResizablePanel>

    <template v-if="!isMobile">
      <ResizableHandle />

      <ResizablePanel :min-size="20" class="pt-5">
        <AlgorithmTabs
          :algorithm="algorithm"
          :block="block"
        />
      </ResizablePanel>
    </template>

  </ResizablePanelGroup>

  <template v-if="isMobile">
    <Separator class="mb-6" />
    
    <AlgorithmTabs
      :algorithm="algorithm"
      :block="block"
    />
  </template>
</template>

<script setup>
  import { ref, computed } from 'vue'

  import { Separator } from '@/components/ui/separator'
  import {
    ResizableHandle,
    ResizablePanel,
    ResizablePanelGroup
  } from '@/components/ui/resizable'
  import { breakpointsTailwind, useBreakpoints } from '@vueuse/core'
  import { createExecutor, createAutoExecutor } from '@/executor'

  import AlgorithmTabs from './AlgorithmTabs.vue'
  import AlgorithmControls from './AlgorithmControls.vue'
  import AlgorithmVariables from './blocks/variables/AlgorithmVariables.vue'
  import Visualizer from './visualizer/Visualizer.vue'

  const props = defineProps({
    algorithmKey: {
      type: String,
      required: true
    },
    visualizable: {
      type: Array,
      default: () => []
    }
  })

  const executor = ref()
  const autoExecutor = ref()
  const algorithm = ref()
  
  function createExecutors (inputs = {}) {
    const created = createExecutor(
      props.algorithmKey, inputs,
      { createRequired: true }
    )
    executor.value = created.executor
    algorithm.value = created.algorithm
    autoExecutor.value = createAutoExecutor(created.executor)
  }

  createExecutors()

  const step = computed(() => executor.value.currentStep ?? {})
  const ctx = computed(() => step.value?.ctx ?? {})
  const block = computed(() => step.value?.block)

  const breakpoints = useBreakpoints(breakpointsTailwind)
  const isMobile = breakpoints.smaller('md')

  function onRestart (inputs) {
    createExecutors(inputs)
  }
</script>