export const isMutable = (data, key) => !!data[key]?.output || !data[key]?.input
export const isInput = (data, key) => !!data[key]?.input
export const isOutput = (data, key) => !!data[key]?.output
export const isMutableInput = (data, key) => isInput(data, key) && isOutput(data, key)
export const isTemporary = (data, key) => !isInput(data, key) && !isOutput(data, key)

/**
 * Получение выходных данных из контекста
 * @param {Object} ctx 
 * @param {Object} data 
 * @returns {Object}
 */
export function getOutputFields (ctx, data) {
  const res = {}
  for (const k in data) {
    if (isOutput(data, k) && ctx[k] !== undefined) {
      res[k] = ctx[k]
    }
  }
  return res
}

/**
 * Получение входных данных из контекста
 * @param {Object} data 
 * @returns {Object}
 */
export function getInputFields (data) {
  const res = {}
  for (const k in data) {
    if (isInput(data, k)) {
      res[k] = data[k]
    }
  }
  return res
}

/**
 * Поиск свободного ключа для нового элемента
 * @param {string} baseKey 
 * @param {Set<string>} usedKeys 
 * @returns {string}
 */
export function findKey (baseKey, usedKeys) {
  // Удаляем числовой суффикс, если он есть
  const match = baseKey.match(/^(.*?)(\d+)$/)
  let root = baseKey
  let startNum = 2
  
  if (match) {
    // Если в ключе уже есть числовой суффикс
    root = match[1]
    startNum = parseInt(match[2]) + 1
    
    // Проверяем, свободен ли корневой ключ
    if (!usedKeys.has(root)) {
      usedKeys.add(root)
      return root
    }
  }
  
  // Пробуем добавлять числа, начиная с startNum
  let counter = startNum
  let newKey = `${root}${counter}`
  
  // Увеличиваем счетчик, пока не найдем свободный ключ
  while (usedKeys.has(newKey)) {
    counter++
    newKey = `${root}${counter}`
  }
  
  usedKeys.add(newKey)
  return newKey
}
