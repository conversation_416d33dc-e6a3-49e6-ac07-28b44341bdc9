import { clone } from '../../utils/clone.js'

/** Базовый минималистичный класс дерева на основе родительских ссылок. */
export class BaseTree {
  _nodes = {}
  _parent = {}
  _root

  _toString (node) {
    if (!node) {
      return ''
    }
    const key = String(node.key)
    const nodeLabel = node.value ? `(${key},${node.value})` : key
    const children = this.getChildNodes(node)
    if (!children.length) {
      return nodeLabel
    }
    return `${nodeLabel}{${children.map(c => this._toString(c)).join(',')}}`
  }

  toString () {
    return this._toString(this.root)
  }

  _nextId () {
    return Math.max(0, ...Object.keys(this._nodes).map(Number)) + 1
  }

  get root () {
    return this._root ? this._nodes[this._root] : null
  }

  get nodes () {
    return Object.values(this._nodes)
  }

  getNode (id) {
    return id ? this._nodes[id] : null
  }

  isRoot (node) {
    return this._root === node.id
  }

  isLeaf (node) {
    return !this.getChildLinks(node).length
  }

  getParentLink (node) {
    return this._parent[node.id] ?? null
  }

  getParentNode (node) {
    return this._parent[node.id]
      ? this._nodes[this.getParentLink(node).id]
      : null
  }

  getChildLinks (node) {
    return Object.values(this._parent).filter(link => link.id === node.id)
  }

  getChildNodes (node) {
    return this.getChildLinks(node).map(link => this.getNode(link.id))
  }

  addNode (node, parent, parentLinkData) {
    node.id = node.id ?? this._nextId()
    const id = node.id

    if (!node.key) {
      throw Error('Узел должен иметь ключ')
    }

    if (this._nodes[id]) {
      throw Error(`Узел с id ${id} уже существует`)
    }

    if (parent) {
      if (id === parent.id) {
        throw Error(`Невозможно создать петлю для узла ${id}`)
      }
      if (!this._nodes[parent.id]) {
        throw Error(`Родительский узел ${parent.id} не существует`)
      }
      if (this._parent[id]) {
        throw Error(`Узел ${id} уже имеет родителя`)
      }

      this._parent[id] = { id: parent.id }
      if (parentLinkData) {
        this._parent[id].data = parentLinkData
      }
    } else {
      if (this._root) {
        throw Error(`Дерево уже имеет корень`)
      } else {
        this._root = id
      }
    }

    this._nodes[id] = node
    return node
  }

  removeNode (node) {
    if (!this.isLeaf(node)) {
      throw Error(`Невозможно удалить узел ${node.key}, у него есть дети`)
    }
    delete this._nodes[node.id]
    delete this._parent[node.id]
    if (node.id === this._root) {
      this._root = undefined
    }
  }

  insert (key, value) {
    this.addNode({ key, value })
  }

  clear () {
    this._root = undefined
    this._nodes = {}
    this._parent = {}
  }

  clone () {
    const res = new this.constructor()
    res._root = this._root
    res._nodes = clone(this._nodes)
    res._parent = clone(this._parent)
    return res
  }
}

/** Дерево с дочерними ссылками. */
export class Tree extends BaseTree {
  _children = {}

  getChildLinks (node) {
    return Object.values(this._children[node.id] ?? {})
  }

  getChildLink (node, label) {
    return this._children[node.id]?.[label] ?? null
  }

  getChildNode (node, label) {
    return this.getNode(this.getChildLink(node, label)?.id)
  }

  addNode (node, parent, childLinkData, parentLinkData) {
    if (!parent) {
      return super.addNode(node)
    }
    let index = 0
    while (this._children[parent.id]?.[index]) {
      index++
    }
    return this.addChild(node, parent, index, childLinkData, parentLinkData)
  }

  addChild (node, parent, label, childLinkData, parentLinkData) {
    const child = super.addNode(node, parent, parentLinkData)
    if (parent) {
      if (!this._children[parent.id]) {
        this._children[parent.id] = {}
      }
      if (this._children[parent.id]?.[label]) {
        throw Error(`Узел ${parent.id} уже имеет ребёнка с меткой ${label}`)
      }
      this._children[parent.id][label] = { id: child.id }
      if (childLinkData) {
        this._children[parent.id][label].data = childLinkData
      }
    }
    return child
  }

  removeNode (node) {
    if (!this.isLeaf(node)) {
      throw Error(`Невозможно удалить узел ${node.key}, у него есть дети`)
    }
    delete this._children[node.id]
    const link = this.getParentLink(node)
    if (link) {
      const children = this._children[link.id]
      const label = Object.keys(children)
        .find(label => children[label].id === node.id)
      delete children[label]
    }
    super.removeNode(node)
  }

  removeChild (node, label) {
    const child = this.getChildNode(node, label)
    if (!child) {
      return
    }
    if (!this.isLeaf(child)) {
      throw Error(`Невозможно удалить узел ${child.key}, у него есть дети`)
    }
    this.removeNode(child)
  }

  clear () {
    super.clear()
    this._children = {}
  }

  clone () {
    const res = super.clone()
    res._children = clone(this._children)
    return res
  }
}
