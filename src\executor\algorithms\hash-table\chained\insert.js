export const hashTableChainedInsert = (keyType, valueType) => ({
    key: `hash-table-chained-${keyType}-${valueType}_insert`,
    label: `Вставка в Chained Hash Table (${keyType}, ${valueType})`,
	description: 'Вставка занчения в хеш-таблицу использующую структуру отдельных цепочек по ключу',
	complexity: {
		time: {
            worst: 'O(n)',
            avg: 'O(1)',
            best: 'O(1)'
        }
	},
	initial: 'start',

	data: {
		hashTable: {
			label: 'HashTableChained',
			description: 'Хеш-таблица на открытых цепочках',
			type: `hash-table-chained<${keyType},${valueType}>`,
			input: true,
			required: true
		},
		key: {
			label: 'Ключ',
			type: keyType,
			input: true,
			required: true
		},
		value: {
			label: 'Значение',
			description: 'Значение, передаваемое с ключем',
			type: valueType,
			input: true, 
            required: true
		},
        index: {
            label: 'Индекс вставки',
            description: 'Индекс, куда вставлять элемент в массиве',
            type: 'integer',
            input: true,
            required: true
        }
	},

	blocks: {
       
        start: {
            type: 'start',
            label: 'Начало вставки',
            description: 'Инициализация процесса добавления элемента в хеш-таблицу',
            next: 'checkCapacity'
        },

        checkCapacity: {
            type: 'condition',
            label: 'Проверка загруженности таблицы',
            description: 'Проверяем, нужно ли увеличивать размер таблицы',
            conditions: [
                {
                    label: 'Нужно расширение',
                    description: 'Текущая загруженность превышает loadFactor',
                    next: 'resizeTable'
                },
                {
                    label: 'Места достаточно',
                    description: 'Таблица может принять новый элемент',
                    next: 'calculateIndex'
                }
            ]
        },

        resizeTable: {
            type: 'action',
            label: 'Увеличение таблицы',
            description: 'Увеличиваем размер таблицы в 2 раза и перехешируем все элементы',
            next: 'calculateIndex'
        },

        calculateIndex: {
            type: 'action',
            label: 'Вычисление индекса',
            description: 'Получаем начальный индекс через хеш-функцию',
            next: 'collisionCheck'
        },

    
        collisionCheck: {
            type: 'condition',
            label: 'Проверка коллизии',
            description: 'Проверяем, свободна ли ячейка',
            conditions: [
                {
                    label: 'Коллизия',
                    description: 'Ячейка занята - применяем линейное пробирование',
                    next: 'linearProbing'
                },
                {
                    label: 'Свободно',
                    description: 'Ячейка свободна - можно вставлять',
                    next: 'insertElement'
                }
            ]
        },

        linearProbing: {
            type: 'action',
            label: 'Линейное пробирование',
            description: 'Ищем следующую свободную ячейку',
            next: 'collisionCheck'
        },

        insertElement: {
            type: 'action',
            label: 'Вставка элемента',
            description: 'Записываем пару ключ-значение в найденную ячейку',
            next: 'finish'
        },

        finish: {
            type: 'finish',
            label: 'Элемент добавлен',
            description: 'Операция вставки завершена успешно'
        }
    },

	validations: [],

	invoke({ hashTable, key, value }) {
        // block: 'checkCapacity'
        // msg: Проверяем вместимость текущей таблицы
        if ((hashTable._numElements + 1) / hashTable._size > hashTable._loadFactor) {
            // block: 'resizeTable'
            // msg: Если загруженность таблицы больше определенного процента, то увеличиваем таблицу в двое
            hashTable._resize(hashTable._size * 2)
        }
        
        // block: 'calculateIndex'
        // msg: Получаем индекс для вставки при помощи хеш-функции
        let index = hashTable.hash(key)
        
        // block: 'collisionCheck'
        // msg: Проверяем наличие коллизии в выбранной позиции
        while (hashTable._table[index] !== null) {
            // block: 'linearProbing'
            // msg: Применяем линейное пробирование для поиска свободной ячейки
            index = (index + 1) % hashTable._size;
        }

        // block: 'insertElement'
        // msg: Найдя свободную позицию, вставляем элемент в таблицу
        hashTable._numElements = hashTable._numElements + 1
        hashTable._table[index] = [key, value]
        
        // block: 'finish'
        // msg: Элемент успешно добавлен в хеш-таблицу
    }
})

export const hashTableChainedInsertAnyAny = hashTableChainedInsert('any', 'any')