import { expect, test } from 'vitest'

import { Tree } from './tree'
import { BinarySearchTree } from './bst'

test('tree to work properly', () => {
  const t = new Tree()
  const n1 = t.addNode({ key: 1 })
  expect(() => t.addNode({ key: 10 })).toThrow()
  const n2 = t.addNode({ key: 2 }, n1)
  const n3 = t.addNode({ key: 3 }, n1)
  const n4 = t.addNode({ key: 4 }, n2)
  const n5 = t.addNode({ key: 5 }, n2)
  const n6 = t.addNode({ key: 6 }, n3)
  const n7 = t.addNode({ key: 7 }, n3)

  expect(t.root).toBe(n1)
  expect(t.isRoot(n1)).toBe(true)
  expect(t.isRoot(n2)).toBe(false)
  expect(t.isRoot(n3)).toBe(false)
  expect(t.isRoot(n4)).toBe(false)
  expect(t.isRoot(n5)).toBe(false)
  expect(t.isRoot(n6)).toBe(false)
  expect(t.isRoot(n7)).toBe(false)

  expect(t.isLeaf(n1)).toBe(false)
  expect(t.isLeaf(n2)).toBe(false)
  expect(t.isLeaf(n3)).toBe(false)
  expect(t.isLeaf(n4)).toBe(true)
  expect(t.isLeaf(n5)).toBe(true)
  expect(t.isLeaf(n6)).toBe(true)
  expect(t.isLeaf(n7)).toBe(true)

  expect(t.getChildLinks(n1)).toEqual([{ id: n2.id }, { id: n3.id }])
  expect(t.getChildNodes(n1)).toEqual([n2, n3])
  expect(t.getChildLinks(n2)).toEqual([{ id: n4.id }, { id: n5.id }])
  expect(t.getChildNodes(n2)).toEqual([n4, n5])
  expect(t.getChildLinks(n3)).toEqual([{ id: n6.id }, { id: n7.id }])
  expect(t.getChildNodes(n3)).toEqual([n6, n7])
  expect(t.getChildLinks(n4)).toEqual([])
  expect(t.getChildNodes(n4)).toEqual([])
  expect(t.getChildLinks(n5)).toEqual([])
  expect(t.getChildNodes(n5)).toEqual([])
  expect(t.getChildLinks(n6)).toEqual([])
  expect(t.getChildNodes(n6)).toEqual([])
  expect(t.getChildLinks(n7)).toEqual([])
  expect(t.getChildNodes(n7)).toEqual([])

  expect(t.getParentLink(n1)).toBeNull()
  expect(t.getParentNode(n1)).toBeNull()
  expect(t.getParentLink(n2)).toEqual({ id: n1.id })
  expect(t.getParentNode(n2)).toBe(n1)
  expect(t.getParentLink(n3)).toEqual({ id: n1.id })
  expect(t.getParentNode(n3)).toBe(n1)
  expect(t.getParentLink(n4)).toEqual({ id: n2.id })
  expect(t.getParentNode(n4)).toBe(n2)
  expect(t.getParentLink(n5)).toEqual({ id: n2.id })
  expect(t.getParentNode(n5)).toBe(n2)
  expect(t.getParentLink(n6)).toEqual({ id: n3.id })
  expect(t.getParentNode(n6)).toBe(n3)
  expect(t.getParentLink(n7)).toEqual({ id: n3.id })
  expect(t.getParentNode(n7)).toBe(n3)

  expect(() => t.removeNode(n1)).toThrow()
  expect(() => t.removeNode(n2)).toThrow()
  expect(() => t.removeNode(n3)).toThrow()
  t.removeNode(n4)
  t.removeNode(n5)
  t.removeNode(n6)
  t.removeNode(n7)

  expect(t.nodes.length).toBe(3)
})

test('binary search tree to work properly', () => {
  const t = new BinarySearchTree()
  const n1 = t.addNode({ key: 1 })
  expect(() => t.addNode({ key: 10 })).toThrow()
  const n2 = t.addLeftChild({ key: 2 }, n1)
  const n3 = t.addRightChild({ key: 3 }, n1)
  const n4 = t.addRightChild({ key: 4 }, n2)
  expect(() => t.addRightChild({ key: 5 }, n2)).toThrow()

  expect(t.root).toBe(n1)
  expect(t.isRoot(n1)).toBe(true)
  expect(t.isRoot(n2)).toBe(false)
  expect(t.isRoot(n3)).toBe(false)
  expect(t.isRoot(n4)).toBe(false)

  expect(t.isLeaf(n1)).toBe(false)
  expect(t.isLeaf(n2)).toBe(false)
  expect(t.isLeaf(n3)).toBe(true)
  expect(t.isLeaf(n4)).toBe(true)

  expect(t.getLeftLink(n1)).toEqual({ id: n2.id })
  expect(t.getLeftNode(n1)).toBe(n2)
  expect(t.getRightLink(n1)).toEqual({ id: n3.id })
  expect(t.getRightNode(n1)).toBe(n3)
  expect(t.getLeftLink(n2)).toBeNull()
  expect(t.getLeftNode(n2)).toBeNull()
  expect(t.getRightLink(n2)).toEqual({ id: n4.id })
  expect(t.getRightNode(n2)).toBe(n4)
  expect(t.getLeftLink(n3)).toBeNull()
  expect(t.getLeftNode(n3)).toBeNull()
  expect(t.getRightLink(n3)).toBeNull()
  expect(t.getRightNode(n3)).toBeNull()
  expect(t.getLeftLink(n4)).toBeNull()
  expect(t.getLeftNode(n4)).toBeNull()
  expect(t.getRightLink(n4)).toBeNull()
  expect(t.getRightNode(n4)).toBeNull()

  expect(() => t.removeLeftChild(n1)).toThrow()
  t.removeRightChild(n2)
  t.removeLeftChild(n1)
  t.removeRightChild(n1)

  expect(t.nodes.length).toBe(1)
  expect(t.root).toBe(n1)
  expect(t.isRoot(n1)).toBe(true)
  t.removeNode(n1)

  expect(t.nodes.length).toBe(0)
  expect(t.root).toBeNull()
})
