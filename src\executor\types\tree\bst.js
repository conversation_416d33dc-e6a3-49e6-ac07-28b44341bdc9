import { Tree } from './tree'

const LEFT = 'left'
const RIGHT = 'right'

/** Двоичное дерево. */
export class BinaryTree extends Tree {
  addNode (node, parent, childLinkData, parentLinkData) {
    if (parent && this.getChildLinks(parent).length >= 2) {
      throw Error(`Узел ${parent.key} уже имеет двух детей`)
    }
    if (parent) {
      if (this.getLeftLink(parent)) {
        return this.addRightChild(node, parent, childLinkData, parentLinkData)
      } else {
        return this.addLeftChild(node, parent, childLinkData, parentLinkData)
      }
    } else {
      return super.addNode(node)
    }
  }

  addLeftChild (node, parent, childLinkData, parentLinkData) {
    const link = this.getLeftLink(parent)
    if (link) {
      throw Error(`Узел ${parent.key} уже имеет левого ребёнка`)
    }
    return this.addChild(node, parent, LEFT, childLinkData, parentLinkData)
  }

  addRightChild (node, parent, childLinkData, parentLinkData) {
    const link = this.getRightLink(parent)
    if (link) {
      throw Error(`Узел ${parent.key} уже имеет правого ребёнка`)
    }
    return this.addChild(node, parent, RIGHT, childLinkData, parentLinkData)
  }

  removeLeftChild (node) {
    this.removeChild(node, LEFT)
  }

  removeRightChild (node) {
    this.removeChild(node, RIGHT)
  }

  getLeftLink (node) {
    return this.getChildLink(node, LEFT)
  }

  getRightLink (node) {
    return this.getChildLink(node, RIGHT)
  }

  getLeftNode (node) {
    return this.getChildNode(node, LEFT)
  }

  getRightNode (node) {
    return this.getChildNode(node, RIGHT)
  }
}

/** Бинарное дерево поиска. */
export class BinarySearchTree extends BinaryTree {
}

export class AVLTree extends BinarySearchTree {
}

const RED = 'red'
const BLACK = 'black'

export class RedBlackTree extends BinarySearchTree {
  _color = {}
}
