import path from 'node:path'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

import compileAlgorithmsPlugin from './src/executor/plugin'

export default defineConfig({
  plugins: [vue(), tailwindcss(), compileAlgorithmsPlugin()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
  },
  test: {
    globals: true,
    environment: 'happy-dom',
    css: true
  }
})
