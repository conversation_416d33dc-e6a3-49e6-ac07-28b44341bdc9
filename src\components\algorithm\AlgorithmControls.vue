<template>
  <Card>
    <CardContent class="flex gap-6">
      <div class="w-32">
        <div class="flex justify-center gap-2">
          <Button
            variant="outline"
            size="icon"
            class="cursor-pointer"
            :disabled="!executor.canBackward"
            @click="executor.backward()"
          >
            <ChevronLeft />
          </Button>

          <Button
            variant="outline"
            size="icon"
            class="cursor-pointer"
            @click="autoExecutor.isRunning
              ? autoExecutor.pause()
              : autoExecutor.start()
            "
          >
            <Play v-if="!autoExecutor.isRunning" />
            <Pause v-else />
          </Button>

          <Button
            variant="outline"
            size="icon"
            class="cursor-pointer"
            :disabled="!executor.canForward"
            @click="executor.forward()"
          >
            <ChevronRight />
          </Button>
        </div>

        <div class="flex justify-center mt-4 text-xs text-muted-foreground">
          {{ executor.currentStepNumber }} / {{ executor.stepsCount }}
        </div>

        <div class="flex justify-center mt-4 text-xs">
          <Tooltip>
            <TooltipTrigger as-child>
              <Button
                variant="ghost"
                size="icon"
                @click="executor.toStart()"
              >
                <ChevronFirst />
              </Button>
            </TooltipTrigger>
            
            <TooltipContent>
              <p>В начало</p>
            </TooltipContent>
          </Tooltip>

          <AlgorithmDataDialog
            :data="data"
            :initial="initial"
            @submit="$emit('restart', $event)"
          >
            <Tooltip>
              <TooltipTrigger as-child>
                <Button
                  variant="ghost"
                  size="icon"
                >
                  <DatabaseBackup />
                </Button>
              </TooltipTrigger>
              
              <TooltipContent>
                <p>Перезапустить с указанными данными</p>
              </TooltipContent>
            </Tooltip>
          </AlgorithmDataDialog>

          <Tooltip>
            <TooltipTrigger as-child>
              <Button
                variant="ghost"
                size="icon"
                @click="executor.finish()"
              >
                <ChevronLast />
              </Button>
            </TooltipTrigger>
            
            <TooltipContent>
              <p>В конец</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <div class="flex-2 border">
        <ScrollArea
          ref="scrollAreaRef"
          style="height: 120px"
        >
          <div
            ref="scrollContainer"
            class="flex flex-col"
          >
            <p class="text-sm text-muted-foreground text-balance pl-3 py-2">
              <b>1.</b> Начало алгоритма
            </p>

            <template v-for="(msg, index) in executor.messages" :key="msg">
              <Separator />
              <p class="text-sm text-muted-foreground text-balance pl-3 py-2">
                <b>{{ index + 2 }}.</b> {{ msg }}
              </p>
            </template>
          </div>
        </ScrollArea>
      </div>
    </CardContent>
  </Card>
</template>

<script setup>
  import { ref, watch, nextTick } from 'vue'

  import {
    Play,
    Pause,
    ChevronLeft,
    ChevronRight,
    ChevronFirst,
    ChevronLast,
    DatabaseBackup
  } from 'lucide-vue-next'

  import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
  import { Card, CardContent } from '@/components/ui/card'
  import { Button } from '@/components/ui/button'
  import { ScrollArea } from '@/components/ui/scroll-area'
  import { Separator } from '@/components/ui/separator'
  import AlgorithmDataDialog from './AlgorithmDataDialog.vue'

  const props = defineProps({
    executor: Object,
    autoExecutor: Object,
    data: Object,
    initial: Object
  })
  defineEmits(['restart'])

  const scrollAreaRef = ref()
  const scrollContainer = ref()

  watch(
    () => props.executor.messages,
    async () => {
      await nextTick()
      scrollAreaRef.value.viewport.scrollTo({
        top: scrollContainer.value.scrollHeight,
        behavior: 'instant'
      })
    }
  )
</script>