import { trimBraces, splitTopLevel } from '../utils'
import { BinarySearchTree } from './bst'
import { bstInsert } from '../../algorithms/bst'

export function parseBst (str, type, { parse }) {
  str = trimBraces(str)
  const keyType = type.items?.length ? type.items[0] : 'any'
  const valueType = type.items?.[1]
  const itemType = valueType
    ? { name: 'pair', items: [keyType, valueType] }
    : keyType
  const items = splitTopLevel(str)
    .map(v => v.trim())
    .map(v => parse(v, itemType))
  const tree = new BinarySearchTree()
  const insert = bstInsert(keyType.name, valueType ? valueType.name : 'any')
  items.forEach(item => {
    valueType
      ? insert.invoke({ tree, ...item })
      : insert.invoke({ tree, key: item })
  })
  return tree
}