import { mount } from '@vue/test-utils'

import AlgorithmInfoSources from './AlgorithmInfoSources.vue'

test('renders correctly', () => {
  const wrapper = mount(AlgorithmInfoSources, {
    props: {
      sources: [
        {
          label: 'Test',
          url: 'https://test.com'
        }
      ]
    }
  })

  expect(wrapper.text()).toContain('Test')
  expect(wrapper.find('a').attributes('href')).toBe('https://test.com')
})