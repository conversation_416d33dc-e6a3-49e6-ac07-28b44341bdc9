# Библиотека алгоритмов

[TOC]

## 1. Основные положения

Библиотека алгоритмов и их пошагового выполнения. Все алгоритмы и генераторы данных описываются в виде JavaScript-объектов с определенной структурой.

**Основные компоненты:**
- `./executor.js` - Реализует пошаговое выполнение алгоритмов. Прогоняет алгоритм от начала до конца, фиксируя значения переменных в определенные моменты. Внешний API этого объекта позволяет перемещаться по истории выполнения вперед и назад, а также получать текущее состояние и лог выполнения.
- `./auto-executor.js` - Автоматически выполняет алгоритм пошагово с заданной задержкой между шагами.
- `./history.js` - Утилитарный класс, хранящий историю изменения контекста.

## 2. Руководство по добавлению алгоритмов

### 2.1. Общая информация

Алгоритмы в системе представляют собой JavaScript-объекты с определенной структурой. Каждый алгоритм должен быть размещен в директории `./algorithms`. Функциональность, связанная с пошаговым выполнением, реализуется за счёт прекомпиляции алгоритмов vite-плагином (файл `./plugin.js`).

Код алгоритма размещается в методе `invoke`, принимающем объект с входными данными и возвращающем объект с выходными данными. Никаких особенных требований к написанию кода алгоритма нет, однако нужно учитывать следующее:
- Локальные переменные объявляются как обычно, но для отслеживания их значений, они должны быть описаны в секции `data` алгоритма.
- Можно использовать вызовы других алгоритмов (их `invoke` методов). В этом случае пошаговое выполнение продолжится внутри вызванного алгоритма.
- Можно использовать вызовы произвольных функций, но их выполнение не будет отслеживаться пошагово, а произойдёт атомарно.
- Информация о шагах алгоритма указывается в виде комментариев в коде (подробнее см. раздел 2.7). Поэтому для предотвращения возможных проблем лучше не использовать произвольные комментарии.

### 2.2. Шаги по добавлению нового алгоритма

1. Создайте новую директорию в `./algorithms/` с именем вашего алгоритма (используйте kebab-case, например `quick-sort`)
2. Создайте файл `index.js` внутри этой директории
3. Определите объект алгоритма с необходимыми свойствами
4. Зарегистрируйте алгоритм в `./algorithms/index.js`

### 2.3. Структура объекта алгоритма

```javascript
export default {
  // Уникальный ключ алгоритма (используется для идентификации)
  key: 'algorithm-name',
  
  // Отображаемое название алгоритма
  label: 'Название алгоритма',
  
  // Описание алгоритма
  description: 'Подробное описание принципа работы алгоритма',
  
  // Информация о сложности алгоритма
  complexity: {
    time: {
      worst: 'O(n²)', // Худший случай
      avg: 'O(n²)',   // Средний случай
      best: 'O(n)'    // Лучший случай
    },
    // Или просто time: 'O(log n)' для постоянной сложности
  },
  
  // Начальный блок алгоритма
  initial: 'start',
  
  // Данные, используемые алгоритмом
  data: {
    // Пример входного массива
    array: {
      label: 'Массив',
      description: 'Описание массива',
      type: 'array',
      input: true,
      required: true,
      output: true,  // Если это выходные данные
    },
    
    // Другие переменные
    counter: {
      description: 'Счетчик операций',
      type: 'integer',
      default: 0,
      output: true
    }
  },
  
  // Блоки алгоритма (шаги выполнения)
  blocks: {
    // Начальный блок
    start: {
      label: 'Инициализация',
      next: 'nextBlock'  // Следующий блок
    },
    
    // Условный блок
    condition: {
      label: 'Проверка условия',
      conditions: [
        {
          label: 'Да',
          next: 'trueBlock'
        },
        {
          label: 'Нет',
          next: 'falseBlock'
        }
      ]
    },
    
    // Финальный блок
    finish: {
      label: 'Завершение'
    }
  },
  
  // Функция выполнения алгоритма
  invoke ({ array }) {
    // Инициализация
    let counter = 0
    // msg: 'Начинаем выполнение алгоритма'
    
    // Логика алгоритма
    if (counter > 0) {
      // msg: 'Условие истинно'
    } else {
      // msg: 'Условие ложно'
    }

    someAction()
    // msg: 'Алгоритм завершен'

    return { counter, array }
  }
}
```

### 2.4. Структура полей данных

Каждое поле в секции `data` может содержать следующие свойства:

- (*) `label` - отображаемое название переменной (используется в интерфейсе)
- (*) `type` - тип данных
- `description` - описание переменной и её назначения
- `default` - значение по умолчанию
- `input` - флаг, указывающий является ли переменная входным значением (true/false)
- `required` (для `input`) - флаг, указывающий на обязательность (true/false)
- `output` - флаг, указывающий является ли переменная выходным значением (true/false)

(*) - обязательное поле

### 2.5. Структура блоков алгоритма

Каждый блок в секции `blocks` может содержать следующие свойства:

- `type` - тип блока (`start`, `action`, `condition`, `finish`, `loop-start`, `loop-end`, `call`). Если не указан, то считается `action`.
- `label` - отображаемое название блока
- `description` - описание блока (отображается в подсказке)
- Для блоков `start`, `action`, `loop-start` и `loop-end`:
  - `next` - ключ следующего блока
- Для условного блока `condition`:
  - `conditions` - массив условий, каждое из которых содержит:
    - `label` - название условия (например, "Да"/"Нет")
    - `next` - ключ следующего блока при выполнении условия
- Для блоков `loop-start` и `loop-end`:
  - `loop` - ключ цикла, должен быть одинаковым для блока начала и конца одного и того же цикла
- Для блока вызова подпрограммы `call`:
  - `algorithm` - ключ вызываемого алгоритма

**Важно:** Стартовый блок должен быть строго один.

### 2.6. Функция выполнения алгоритма

Функция `invoke` является основной функцией выполнения алгоритма. Она принимает объект с входными переменными и возвращает объект с выходными переменными.

Пример использования:
```javascript
invoke({ array, needle }) {
  // Инициализация
  let left = 0
  let right = array.length - 1
  
  // block: 'init'
  // msg: 'Инициализируем границы диапазона всей длиной массива'
  
  // Логика алгоритма
  while (left <= right) {
    // Вычисления
    let mid = Math.floor((left + right) / 2)
    
    // block: 'calcMid'
    // msg: `Выбираем опорным элемент ${array[mid]} (по индексу ${mid})`
    
    // Проверка условий и дальнейшие действия
    if (array[mid] === needle) {
      // block: 'found'
      // msg: 'Опорный элемент оказался искомым'
      return { index: mid }
    }
    
    // ...
  }
}
```

Для описания шагов в нужных местах оставляются комментарии формата `<ключ>: <значение>`. Значения - любой валидный js-код. Несколько идущих подряд подобных комментариев интерпретируются как информация об одном шаге.

Поддерживаемые ключи:
- `block` - ключ блока, в котором произошел шаг (подсвечивается на блок-схеме и в исходном коде)
- `msg` - сообщение, которое нужно отобразить в логе выполнения
- **TODO**: подсветка данных и прочая мета-информация

### 2.7. Регистрация алгоритма

После создания алгоритма его необходимо зарегистрировать в `src/simulator/algorithms/index.js`:

```javascript
import binarySearch from "./binary-search"
import insertionSort from './insertion-sort'
import yourAlgorithm from './your-algorithm'

const algorithms = {
  [binarySearch.key]: binarySearch,
  [insertionSort.key]: insertionSort,
  [yourAlgorithm.key]: yourAlgorithm
}
```

**ВАЖНО!**
- Ключ алгоритма должен быть уникальным и не содержать символов, запрещенных в CSS-селекторах. Т.е. допускаются только латинские буквы, цифры, дефисы и подчеркивания.
- Если алгоритм может выполняться на разных типах данных, то для каждого типа должен быть создан отдельный алгоритм с уникальным ключом. Проще всего это сделать с помощью функции-фабрики:
```javascript
const create = itemType => ({
  key: `insertion-sort-${itemType}`,
  label: `Сортировка вставками (${itemType})`,
  data: {
    array: {
      type: `array<${itemType}>`,
      // ...
    }
  },
  // ...
})
export const insertionSortNumber = create('number')
export const insertionSortString = create('string')
``` 

## 3. Система типов

В описаниях алгоритмов типы переменных указываются в соответствие с указанной ниже системой, реализованной в `./types`.

### 3.1. Базовые концепции

Требования к типам переменных формируются с участием следующих понятий: тип, подтип, тип элементов, атрибуты.

**Тип**

Поддерживаемые типы данных:
- Примитивные:
  - `number` - произвольное число
    - `integer` - целое число
  - `boolean` - логическое значение
  - `string` - строка
    - `char` - символ (строка из 1 символа)
- Структуры данных:
  - `array` - массив
  - `pair` - пара ключ-значение
  - `graph` - граф
  - `tree` - дерево
    - `bst` - бинарное дерево поиска
      - `avl` - AVL-дерево
      - `rbtree` - красно-чёрное дерево
    - `trie` - префиксное дерево (бор, trie)
  - `hash-table` - хэш-таблица

Структуры данных отличаются от примитивных типов тем, что могут иметь подтипы и типы элементов.

**Подтип**

Подтип - это подвид указанной структуры данных, указывающий на наличие у неё какого-то качественного свойства или поведения. Примеры: взвешенность у графа, направленность у графа, тип разрешения коллизий у хэш-таблицы.

**Тип элементов**

Т.к. структуры данных являются составлным типом, то для них может быть определён тип элементов. Для массива это элементы массива, для графа - значения в вершинах, для хэш-таблицы - хранимые значения и т.д. Подтипы также может предполагать возможность указания типа элементов, появляющихся у структуры при отнесении к этому подтипу (например, тип весов у взвешенного графа).

**Атрибуты**

Атрибуты - это такие свойства, которые не присущи типу данных, а определяются конкретикой содержимого и могут меняться при манипуляциях с ними. Примеры: отсортированность массива, наличие циклов в графе, неотрицательность чисел. При описании входных переменных они задают правила валидации.

### 3.2. Строковый синтаксис

Полное описание требований к переменной возможно в двух синтаксисах: кратком строковом и объектном.

В строковом виде после описания базового типа (если требуется) в скобках через запятую указываются подтипы. Типы элементов, если они требуются, указываются в треугольных скобках после названия типа через запятую. Если тип элемента не указан, то он считается произвольным. Атрибуты указываются у типа или подтипов через точку и после скобок (если указаны подтипы).

**Примеры:** `array`, `array<number>`, `array<array<number>.sorted>` (массив отсортированных массивов чисел), `graph<char>(weighted<number>,directed.acyclic)` (взвешенный ориентированный граф без циклов с числовыми вершинами и символами в узлах), `bst<number,hash<string,string>(chaining)>` (бинарное дерево поиска с числовыми ключами и значениями в виде хэш-таблиц с методом цепочек и строковыми ключами и значениями)

### 3.3. Объектный синтаксис

Тип задаётся объектом с обязательным полем `name`, опциональным `sub` для массива подтипов, опциональным `items` для массива типов элементов и опциональным `attrs` для массива атрибутов. На любом уровне этой иерархии очередной тип допустимо описать в строковом синтаксисе.

**Примеры:**
```javascript
// Отсортированный массив чисел
{
  name: 'array',
  items: [{ name: 'number' }],
  attrs: ['sorted']
}

// Массив отсортированных массивов чисел
{
  name: 'array',
  items: [{
    name: 'array',
    items: [{ name: 'number' }],
    attrs: ['sorted']
  }]
}

// Массив отсортированных массивов чисел c применением строкового синтаксиса
{
  name: 'array',
  items: ['array<number>.sorted']
}

// Взвешенный ориентированный граф без циклов с числовыми вершинами и символами в узлах
{
  name: 'graph',
  items: [{ name: 'char' }],
  sub: [
    {
      name: 'weighted',
      items: [{ name: 'integer' }]
    },
    { name: 'directed', attrs: ['acyclic'] }
  ]
}

// Взвешенный ориентированный граф без циклов с числовыми вершинами и символами в узлах c применением строкового синтаксиса
{
  name: 'graph',
  items: [{ name: 'char' }]
  sub: ['weighted<integer>', 'directed.acyclic']
}

// Бинарное дерево поиска с числовыми ключами и значениями в виде хэш-таблиц с методом цепочек и строковыми ключами и значениями
{
  name: 'bst',
  items: [
    { name: 'number' },
    {
      name: 'hash',
      items: [{ name: 'string' }, { name: 'string' }],
      sub: [{ name: 'chaining' }]
    }
  ]
}
```

### 3.4. Поддерживаемые атрибуты данных

| Тип | Атрибут | Описание |
| --- | --- | --- |
| `number`, `integer` | `.nonzero` | Ненулевое значение |
|| `.positive` | Положительное значение |
|| `.nonnegative` | Неотрицательное значение |
| `string`, `char` | `.uppercase` | Значение в верхнем регистре |
|| `.lowercase` | Значение в нижнем регистре |
| `array` | `.sorted` | Массив отсортирован |
|| `.unique` | Массив содержит только уникальные значения |

### 3.5. Поддерживаемые подтипы и элементы данных

| Тип | Подтип | Элементы |
| --- | --- | --- |
| `pair<K,V>` || `K` - тип ключа, `V` - тип значения |
| `graph<V>` || `V` - Тип элементов узлов |
|| `weighted<E>` | Взвешенный граф</br> `E` - Тип элементов ребер |
|| `directed` | Ориентированный граф |

### 3.6. Валидация данных

Для валидации данных используется библиотека [Zod](https://zod.dev/). Она позволяет описывать схемы данных и проверять их на соответствие.

Доступны следующие утилитарные функции (из `./types`):
- Для получения схем валидации:
  - `toSchema(type)` - получение схемы валидации для переменной по её типу (в строковом или объектном синтаксисе)
  - `dataToSchema(data)` - получение схемы валидации для объекта с переменными по их спецификации (по объекту `data` из описания алгоритма или произвольному в том же синтаксисе)
- Для валидации по схеме:
  - `validateBySchema(value, schema)` - валидация значения по схеме
- Для получения схемы и последующей валидации:
  - `validateByType(value, type)` - валидация значения по его типу (в строковом или объектном синтаксисе)
  - `validateByData(value, data)` - валидация объекта с переменными по их спецификации (по объекту `data` из описания алгоритма или произвольному в том же синтаксисе)

Валидация входных параметров осуществляется при создании экземпляра `Executor`, и также она может пригодиться для предварительной проверки при заполнении форм.

При реализации новых структур данных необходимо реализовать для них генерацию схемы валидации по описанию типа и зарегистрировать её в `./types/schema.js`. Схема должна валидировать тип переменной, а также типы элементов структуры. Здесь может помочь функция `instanceSchema(klass, fieldSchemas)` из `./types/utils.js`, которая проверяет, что значение является экземпляром указанного класса и, при наличии `fieldSchemas`, валидирует его поля по указанным схемам. Примеры использования можно посмотреть в реализациях для уже существующих структур.

```javascript
// ./types/schema.js
import { toYourStructureSchema } from './your-structure'

const converters = {
  // ...
  yourStructure: toYourStructureSchema
}
```

### 3.7. Генерация случайных данных

Для генерации случайных данных используется функция `create(type)` (из `./types`), которая принимает тип данных в строковом или объектном синтаксисе и возвращает случайное значение соответствующего типа.

При реализации новых структур данных необходимо реализовать для них генерацию случайных значений по описанию типа и зарегистрировать её в `./types/create.js`.

```javascript
// ./types/create.js
import { createYourStructure } from './your-structure'

const factory = {
  // ...
  yourStructure: createYourStructure
}
```

### 3.8. Базовые реализации структур данных

Для структур данных реализованы базовые классы, которые можно использовать в алгоритмах. Базовые реализации не содержат сколько-либо сложной бизнес-логики, а определяют особенности представления в памяти и примитивные методы для манипуляций с ним. Любые операции, реализующие интерфейс структуры (поиск, вставка и т.д.) должны быть в виде отдельных алгоритмов, доступных для пошагового выполнения.

Все структуры данных реализованы также в `./types`.

**ВАЖНО!** При реализации новых структур нужно соблюсти следующие правила:
- Должен быть метод `clone()` для клонирования структуры данных. Он должен быть глубоким, т.е. делать копию не только самого объекта, но и всех его элементов.
- Нельзя использовать нативные приватные свойства (синтаксис с решёткой), т.к. они не дружат с Proxy, который может применяться в системах реактивности.
- Необходимо реализовать генерацию схемы валидации для новой структуры и зарегистрировать её в `./types/schema.js`.
- Необходимо реализовать генерацию случайного значения для новой структуры и зарегистрировать её в `./types/create.js`.
- Может быть реализован метод `toString()` для отображения структуры данных в виде строки.

#### 3.8.1. Graph (граф)

Базовая реализация графа со списками инцидентности: для каждого узла хранится список инцидентных ребер.

```javascript
const graph = new Graph({
  directed: true // ориентированный (по умолчанию - нет)
})

// Структура объекта узла:
{
  id: 1, // идентификатор
  data: 'A' // хранимые данные
}

// Структура объекта ребра:
{
  id: 1, // идентификатор узла с которым соединён этот узел
  id2: 2, // идентификатор текущего узла (в чьём списке инцидентности содержится это ребро)
  data: 10 // хранимые данные (вес ребра)
}

// Основные поля:
graph.isDirected // является ли граф ориентированным
graph.nodes // список узлов
graph.links // список ребер

// Основные методы:
graph.getNode(id) // получить узел по идентификатору
graph.getLinks(node) // получить список ребер, исходящих из узла
graph.getLinkedNodes(node) // получить список смежных узлов
graph.getLink(node1, node2) // получить ребро из узла 1 в узел 2
graph.isLinked(node1, node2) // проверить, есть ли ребро из узла 1 в узел 2
graph.getReversedLink(link) // получить обратное ребро (для ориентированных графов)
graph.addNode(data) // добавить узел
graph.removeNode(node) // удалить узел
graph.addLink(node1, node2, data) // добавить ребро из узла 1 в узел 2
graph.removeLink(node1, node2) // удалить ребро
graph.clear() // очистить граф
graph.clone() // клонировать граф
```

#### 3.8.2. Tree (дерево)

Универсальная реализация дерева. Дочерние ссылки имеют произвольные строковые или числовые метки (label). На базе этого дерева можно построить деревья с произвольной арностью. Дочерние и родительские ссылки могут хранить произвольные данные.

```javascript
const tree = new Tree()

// Основные поля:
tree.root // корень дерева
tree.nodes // список узлов

// Структура объекта узла:
{
  id: 1, // идентификатор узла
  key: 'key', // ключ
  value: 'value' // значение
}

// Структура родительской ссылки:
{
  id: 1, // идентификатор родительского узла
  data: 'value' // хранимые данные
}

// Структура дочерней ссылки:
{
  id: 1, // идентификатор дочернего узла
  data: 'value' // хранимые данные
}

// Основные методы:
tree.getNode(id) // получить узел по идентификатору
tree.isRoot(node) // является ли узел корнем
tree.isLeaf(node) // является ли узел листом
tree.getParentLink(node) // получить ребро к родительскому узлу
tree.getParentNode(node) // получить родительский узел
tree.getChildLinks(node) // получить список рёбер, соединяющих с дочерними узлами
tree.getChildLink(node, label) // получить ребро к дочернему узлу по метке
tree.getChildNode(node, label) // получить дочерний узел по метке
tree.addNode(node, parent, childLinkData, parentLinkData) // добавить узел; если parent === null, то узел становится корнем, иначе - добавляется как дочерний узел по минимальной свободной целочисленной метке; аргументы childLinkData и parentLinkData содержат данные, хранящиеся в дочерней и родительской ссылках (если нужно)
tree.addChild(node, parent, label, childLinkData, parentLinkData) // добавить дочерний узел по указанной метке
tree.removeNode(node) // удалить узел
tree.removeChild(node, label) // удалить дочерний узел по метке
tree.clear() // очистить дерево
tree.clone() // клонировать дерево
```

#### 3.8.3. BST (бинарное дерево поиска)

Базовая реализация бинарного дерева поиска. Является наследницей `Tree`, поэтому имеет все его методы. Левый и правый ребёнок имеют метки `left` и `right`.

```javascript
const bst = new BinarySearchTree()

// Основные методы (помимо унаследованных от Tree):
bst.getLeftLink(node) // получить ребро к левому дочернему узлу
bst.getLeftNode(node) // получить левый дочерний узел
bst.getRightLink(node) // получить ребро к правому дочернему узлу
bst.getRightNode(node) // получить правый дочерний узел
bst.addLeftChild(node, parent, childLinkData, parentLinkData) // добавить левый дочерний узел
bst.addRightChild(node, parent, childLinkData, parentLinkData) // добавить правый дочерний узел
bst.removeLeftChild(node) // удалить левый дочерний узел
bst.removeRightChild(node) // удалить правый дочерний узел
```

## 4. Тестирование

Для тестирования алгоритмов используется библиотека [Vitest](https://vitest.dev/). Файлы тестов должны называться `<название>.test.js` и располагаться в той же директории, что и тестируемый алгоритм или модуль, а также иметь то же название.
