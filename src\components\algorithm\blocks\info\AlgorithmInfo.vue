<template>
  <Table>
    <TableBody>
      <TableRow>
        <TableHead>Название</TableHead>
        <TableCell>{{ algorithm.label }}</TableCell>
      </TableRow>

      <TableRow v-if="algorithm.complexity?.time">
        <TableHead class="whitespace-normal">Сложность по времени</TableHead>
        <TableCell>
          <AlgorithmInfoComplexity :complexity="algorithm.complexity.time" />
        </TableCell>
      </TableRow>

      <TableRow v-if="algorithm.complexity?.memory">
        <TableHead class="whitespace-normal">Сложность по памяти</TableHead>
        <TableCell>
          <AlgorithmInfoComplexity :complexity="algorithm.complexity.memory" />
        </TableCell>
      </TableRow>

      <TableRow v-if="algorithm.sources">
        <TableHead>Источники</TableHead>
        <TableCell>
          <AlgorithmInfoSources :sources="algorithm.sources" />
        </TableCell>
      </TableRow>

      <TableRow>
        <TableCell class="whitespace-normal pt-6" colspan="2">
          {{ algorithm.description }}
        </TableCell>
      </TableRow>

    </TableBody>
  </Table>
</template>

<script setup>
  import {
    Table,
    TableBody,
    TableHead,
    TableCell,
    TableRow
  } from '@/components/ui/table'
  import AlgorithmInfoComplexity from './AlgorithmInfoComplexity.vue'
  import AlgorithmInfoSources from './AlgorithmInfoSources.vue'

  const props = defineProps({
    algorithm: Object
  })
</script>