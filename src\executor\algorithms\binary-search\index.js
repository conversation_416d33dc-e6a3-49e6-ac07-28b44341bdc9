export const binarySearch = itemType => ({
	key: `binary-search-${itemType}`,
	label: `Би<PERSON>рный поиск (${itemType})`,
	description: 'Поиск в отсортированном массиве. Основан на методе деления отрезка пополам и повторении поиска в одной из половин.',
	complexity: {
		time: 'O(log n)'
	},
	initial: 'bs:start',

	data: {
		array: {
			label: 'Массив',
			description: 'Отсортированный массив, по которому осуществляется поиск',
			type: `array<${itemType}>.sorted`,
			input: true,
			required: true
	  },
		needle: {
			label: 'Искомое',
			description: 'Искомое значение',
			type: itemType,
			input: true,
			required: true
		},
		index: {
			label: 'Индекс',
			description: 'Индекс найденного значения или -1 в случае неудачи',
			type: 'integer',
			output: true
		},
		iterations: {
			label: 'Итераций',
			description: 'Количество итераций',
			type: 'integer',
      default: 0,
			output: true
		},
		left: {
			label: 'Левая граница',
			description: 'Индекс левой границы диапазона',
			type: 'integer',
			input: true
		},
		right: {
			label: 'Правая граница',
			description: 'Индекс правой граница диапазона',
			type: 'integer',
			input: true
		},
		mid: {
			label: 'Опорный элемент',
			description: 'Индекс опорного элемента',
			type: 'integer'
		}
	},

	blocks: {
		'bs:start': {
			label: 'left = 0\nright = N - 1',
			next: 'bs:setSupport'
		},
		'bs:setSupport': {
			label: 'mid = floor[(left + right) / 2]',
			next: 'bs:checkSupport'
		},
		'bs:checkSupport': {
			label: 'array[mid] == needle ?',
      conditions: [
        {
					label: 'Да',
          next: 'finish'
				},
        {
					label: 'Нет',
          next: 'bs:cmpSupport'
				}
      ]
		},
		'bs:cmpSupport': {
			label: 'needle < array[mid] ?',
      conditions: [
        {
					label: 'Да',
          next: 'bs:jumpLeft'
				},
        {
					label: 'Нет',
          next: 'bs:jumpRight'
        }
      ]
		},
		'bs:jumpLeft': {
			label: 'right = mid - 1',
			next: 'bs:checkEdges'
		},
		'bs:jumpRight': {
			label: 'left = mid + 1',
			next: 'bs:checkEdges'
		},
		'bs:checkEdges': {
			label: 'right >= left ?',
			conditions: [
        {
					label: 'Да',
          next: 'bs:setSupport'
				},
				{
					label: 'Нет',
          next: 'notFound'
				}
			]
		},
    finish: {
      label: 'Найдено'
    },
    notFound: {
      label: 'Не найдено'
    }
	},

	invoke ({ array, needle, left, right }) {
		let iterations = 0
		left ??= 0
		right ??= array.length - 1
		// msg: 'Инициализируем границы диапазона всей длиной массива'
	
		while (left <= right) {
			let mid = Math.floor((left + right) / 2)
			iterations += 1
			// msg: `Выбираем опорным элемент ${array[mid]} (по индексу ${mid})`
	
			if (array[mid] === needle) {
				// msg: 'Опорный элемент оказался искомым'

				return { index: mid, iterations }
			} else {
				// msg: 'Опорный элемент не равен искомому'

				if (needle < array[mid]) {
					// msg: 'По результатам сравнения искомый элемент лежит по левую сторону'

					right = mid - 1
					// msg: 'Сдвигаем правый край диапазона'
				} else {
					// msg: 'По результатам сравнения искомый элемент лежит по правую сторону'

					left = mid + 1
					// msg: 'Сдвигаем левый край диапазона'
				}
			}
		}
		// msg: 'Правая граница сдвинулась левее левой, значит поиск завершён неудачей'
		return { index: -1, iterations }
	}
})

export const binarySearchNumber = binarySearch('number')
export const binarySearchString = binarySearch('string')