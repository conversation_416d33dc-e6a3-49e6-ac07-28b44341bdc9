import { expect, test } from 'vitest'

import { exponentialSearchNumber } from '.'

test('exponential search to work properly', () => {
  let result = null
  const array = [2, 4, 7, 9, 12, 15, 19, 23, 26, 30, 31, 33, 35, 37, 42, 43, 44, 50, 52, 55, 65, 72, 89, 101]
  for (let i = 0; i < array.length; i++) {
    result = exponentialSearchNumber.invoke({ array, needle: array[i] })
    expect(result.index).toBe(i)
  }

  result = exponentialSearchNumber.invoke({ array, needle: 20 })
  expect(result.index).toBe(-1)

  result = exponentialSearchNumber.invoke({ array, needle: 90 })
  expect(result.index).toBe(-1)
})