import { z } from 'zod'

function applyAtributes (schema, type) {
  if (!type.attrs?.length) {
    return schema
  }
  if (type.attrs.includes('uppercase')) {
    schema = schema.uppercase()
  } else if (type.attrs.includes('lowercase')) {
    schema = schema.lowercase()
  }
  return schema
}

export function toStringSchema (type) {
  return applyAtributes(z.string(), type)
}

export function toCharSchema (type) {
  return applyAtributes(z.string().min(1).max(1), type)
}

export function createString (type) {
  const length = Math.floor(Math.random() * 6) + 3
  let res = ''
  for (let i = 0; i < length; i++) {
    res += createChar(type)
  }
  return res
}

export function createChar (type) {
  const c = String.fromCharCode(Math.floor(Math.random() * 26) + 97)
  if (type.attrs?.includes('lowercase')) {
    return c.toLowerCase()
  }
  return c.toUpperCase()
}

export function parseString (value) {
  return String(value)
}

export function parseChar (value) {
  return String(value).slice(0, 1)
}