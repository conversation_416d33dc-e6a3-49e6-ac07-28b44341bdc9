<template>
  <div ref="codeRef">
    <CodeEditor
      :value="code"
      line-nums
      read-only
      wrap
      width="auto"
      font-size="14px"
      theme="base16-grayscale-dark"
    />
  </div>
</template>

<script setup>
  import { watch, useTemplateRef, onMounted } from 'vue'

  import hljs from 'highlight.js'
  import CodeEditor from "simple-code-editor"

  const props = defineProps({
    code: Object,
    highlight: String
  })

  const codeRef = useTemplateRef('codeRef')

  function highlightComment (v) {
    const comments = codeRef.value.querySelectorAll('.hljs-comment')
    comments.forEach(c => {
      if (v && c.textContent.includes(v)) {
        c.classList.add('highlighted')
      } else {
        c.classList.remove('highlighted')
      }
    })
    
  }

  watch(() => props.highlight, highlightComment)
  onMounted(() => highlightComment(props.highlight))
</script>

<style>
  .hljs-comment.highlighted {
    background: var(--primary);
  }
</style>