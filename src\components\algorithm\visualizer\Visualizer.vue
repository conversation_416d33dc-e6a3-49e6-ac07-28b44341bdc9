<template>
  <ArrayVisualizer
    v-if="typeName === 'array'"
    :array="value"
    show-indexes
  />
</template>

<script setup>
  import { computed } from 'vue'

  import ArrayVisualizer from './array/ArrayVisualizer.vue'

  import { parseTypeString } from '@/executor'

  const props = defineProps({
    type: {
      type: [String, Object],
      required: true
    },
    value: {
      type: [Object, Array, Number, String]
    }
  })

  const typeName = computed(
    () => parseTypeString(props.type).name
  )
</script>