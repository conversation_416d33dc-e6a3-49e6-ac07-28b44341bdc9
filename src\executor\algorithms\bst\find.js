export const bstFind = (keyType, valueType) => ({
	key: `bst-${keyType}-${valueType}_find`,
	label: `Поиск в BST (${keyType}, ${valueType})`,
	description: 'Поиск значения в бинарном дереве поиска по ключу',
	complexity: {
		time: 'O(log n)'
	},
	initial: 'start',

	data: {
		tree: {
			label: 'BST',
			description: 'Бинарное дерево поиска',
			type: `bst<${keyType},${valueType}>`,
			input: true,
			required: true
		},
		key: {
			label: 'Ключ',
			type: keyType,
			input: true,
			required: true
		},
		node: {
			label: 'Текущий узел',
			description: 'Текущий узел, который проверяется на совпадение',
			type: 'object'
		},
		parent: {
			label: 'Родитель текущего узла',
			type: 'object'
		},
		left: {
			label: 'Ребёнок слева',
			type: 'boolean'
		},
		value: {
			label: 'Найденное значение',
			description: 'Значение, найденное в дереве или null в случае неудачи',
			type: valueType,
			output: true
		}
	},

	blocks: {},

	validations: [],

	invoke ({ tree, key }) {
		let parent = null
		let left = null
		let node = tree.root
		// msg: 'Начинаем поиск с корня дерева'

		while (node) {
			// msg: `Смотрим на узел ${node.key}`

			if (node.key === key) {
				// msg: 'Узел совпадает с искомым'

				return { node, value: node.value, parent, left }
			} else {
				// msg: 'Узел не совпадает с искомым'

				if (key < node.key) {
					// msg: 'Искомое меньше значения узла, идём влево'

					parent = node
					left = true
					node = tree.getLeftNode(node)
				} else {
					// msg: 'Искомое больше значения узла, идём вправо'

					parent = node
					left = false
					node = tree.getRightNode(node)
				}
			}
		}
		// msg: 'Искомый ключ не найден в дереве'

		return {
			node, value: null, parent, left
		}
	}
})

export const bstFindStringString = bstFind('string', 'string')
export const bstFindStringNumber = bstFind('string', 'number')
export const bstFindNumberString = bstFind('number', 'string')
export const bstFindNumberNumber = bstFind('number', 'number')
