<template>
  <div class="mermaid-flowchart">
    <AlgorithmFlowchartStyle :current="current" />

    <TooltipDynamic
      selector="g.node"
      :content="el => el.attributes.title?.value"
      :active="tooltipActive"
    >
      <div
        ref="mermaidRef"
        class="mermaid-container flex justify-center"
      />
    </TooltipDynamic>
  </div>
</template>

<script setup>
  import { ref, onMounted, watch, computed } from 'vue'
  import mermaid from 'mermaid'

  import { blocksToFlowchartMermaid } from '@/executor'
  import { TooltipDynamic } from '@/components/ui/tooltip-dynamic'
  import AlgorithmFlowchartStyle from './AlgorithmFlowchartStyle.vue'

  const props = defineProps({
    blocks: Object,
    current: String
  })

  const tooltipActive = ref(false)
  const mermaidRef = ref(null)
  const diagramId = `mermaid-diagram-${Date.now()}`

  const mermaidDefinition = computed(
    () => blocksToFlowchartMermaid(props.blocks)
  )

  async function renderSvg (definition, config = {}) {
    const { theme = 'dark', curve = 'basis' } = config

    try {
      mermaid.initialize({
        startOnLoad: false,
        theme,
        flowchart: {
          // useMaxWidth: false,
          htmlLabels: true,
          securityLevel: 'loose',
          curve
        }
      })
      const { svg } = await mermaid.render(diagramId, definition)
      return svg
    } catch (error) {
      console.error('Ошибка при рендеринге Mermaid диаграммы:', error)
      console.log('Определение диаграммы:', definition)
    }
  }

  async function render () {
    if (!mermaidRef.value || !mermaidDefinition.value) {
      return
    }
    tooltipActive.value = false
    mermaidRef.value.innerHTML = ''
    const svg = await renderSvg(mermaidDefinition.value)
    mermaidRef.value.innerHTML = svg
    tooltipActive.value = true
  }

  onMounted(render)

  watch(() => props.blocks, render, { deep: true })
</script>

<style scoped>
  .mermaid-flowchart {
    width: 100%;
    display: flex;
    justify-content: center;
    overflow: hidden;
  }

  .mermaid-container {
    width: 100%;
    max-height: calc(100vh - 140px);
    overflow: hidden;
  }
</style>
