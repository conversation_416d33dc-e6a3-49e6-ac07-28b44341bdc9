import {
  Home,
  ListStart,
  BookA,
  Box,
  Ellipsis,
  ScanSearch,
  SortAsc,
  Waypoints,
  Workflow
} from "lucide-vue-next"

export default [
  {
    categorized: false,
    items: [
      {
        title: 'Главная',
        to: '/',
        icon: Home
      }
    ]
  },

  {
    title: "Структуры данных",
    categorized: true,
    items: [
      {
        title: 'Очередь с приоритетом',
        icon: ListStart,
        items: [
          {
            title: 'Бинарная куча',
            to: '/'
          }
        ]
      },
      {
        title: 'Словарь',
        icon: BookA,
        items: [
          {
            title: 'Бинарное дерево поиска',
            to: '/'
          },
          {
            title: 'Хеш-таблица',
            to: '/'
          },
          {
            title: 'B-дерево',
            to: '/'
          },
          {
            title: 'Префиксное дерево (бор, trie)',
            to: '/'
          }
        ]
      },

      {
        title: 'Многомерные данные',
        icon: Box,
        items: [
          {
            title: 'kD дерево',
            to: '/'
          }
        ]
      },

      {
        title: 'Прочее',
        icon: Ellipsis,
        items: [
          {
            title: 'Система непересекающихся множеств',
            to: '/'
          }
        ]
      }
    ]
  },

  {
    title: "Алгоритмы",
    categorized: true,
    items: [
      {
        title: 'Поиск',
        icon: ScanSearch,
        items: [
          {
            title: 'Бинарный поиск',
            to: '/'
          },
          {
            title: 'Экспоненциальный поиск',
            to: '/'
          }
        ]
      },
      {
        title: 'Сортировка',
        icon: SortAsc,
        items: [
          {
            title: 'Вставками',
            to: '/'
          },
          {
            title: 'Быстрая',
            to: '/'
          },
          {
            title: 'Слияниями',
            to: '/'
          }
        ]
      },
      {
        title: 'Поиск в графе',
        icon: Workflow,
        items: [
          {
            title: 'Обходы графа',
            to: '/'
          },
          {
            title: 'Волновой алгоритм',
            to: '/'
          },
          {
            title: 'A*',
            to: '/'
          },
          {
            title: 'Дейкстра',
            to: '/'
          }
        ]
      },
      {
        title: 'Максимальный поток',
        icon: Waypoints,
        items: []
      },
      {
        title: 'Прочие на графе',
        icon: Waypoints,
        items: []
      }
    ]
  }
]