<template>
  <div v-if="isSingle"><b>{{ complexity }}</b></div>

  <template v-else>
    <div v-if="complexity.worst">
      Худ<PERSON>ий случай - <b>{{ complexity.worst }}</b>
    </div>
    
    <div v-if="complexity.avg">
      Средний случай - <b>{{ complexity.avg }}</b>
    </div>

    <div v-if="complexity.best">
      Луч<PERSON>ий случай - <b>{{ complexity.best }}</b>
    </div>
  </template>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    complexity: [Object, String]
  })
  const isSingle = computed(() => typeof props.complexity === 'string')
</script>