import { z } from 'zod'

import { instanceSchema } from '../utils.js'
import { HashTable, HashTableChained, HashTableOpenAddressing } from './hash.js'
import { toPairSchema } from '../pair/index.js'

const subType = {
  addressing: (schemas) => {
    schemas.type = z.literal('addressing')
    schemas.class = HashTableOpenAddressing
  },
  chained: (schemas) => {
    schemas.type = z.literal('chained')
    schemas.class = HashTableChained
  }
}

/**
 * 
 * @param {*} type 
 * {
 *  items : []
 *     {name : 'keyType'}, 
 *     {name : 'valueType}'
 *  ]
 *  sub : [
 *      {name : 'chained'}
 *  ]
 * }
 * @param {Function} toSchema 
 * @param {Function} parseType
 * @returns 
 * {
        entries: z.array(
            z.object({
            key: z.string(),
            value: z.number().int()
            }).passthrough()
        ),
        type: z.literal('chained')
    }
 */
export function toHashTableSchema (type, { toSchema, parseType }) {
    const schemas = {}

    const pairSchema = toPairSchema(type, { toSchema })

    schemas.pairs = z.array(pairSchema.passthrough())

    if (type.sub?.length) {
        type.sub.forEach(element => {
            sub = parseType(sub)
            const subConverter = subType[sub.name]
            if (!subConverter) {
                throw new Error(`Неизвестный подтип ${sub.name}`)
            }
            subConverter(schemas, sub, toSchema)
        });
    }

    const cls = schemas.class || HashTableChained
    delete schemas.class

    return instanceSchema(cls, schemas)
}

