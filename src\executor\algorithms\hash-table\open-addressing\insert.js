export const hashTableOpenAddressingInsert = (keyType, ValueType) => ({
    key: "hash-table-open-addressing-insert",
    label: "Вставка в HashTable Open Addressing",
    description: "Добавление элемента в хеш-таблицу с открытой адресацией",
    complexity: {
        time: {
            worst: "O(n)",
            avg: "O(1)",
            best: "O(1)"
        }
    },
    initial: "start",

    data: {
        hashTable: {
            label: "HashTableOpenAddressing",
            description: "Хеш-таблица с открытой адресацией",
            type: "hash-table-open-addressing",
            input: true,
            required: true
        },
        key: {
            label: "Ключ",
            description: "Ключ для вставки",
            type: "any",
            input: true,
            required: true
        },
        value: {
            label: "Значение",
            description: "Значение для вставки",
            type: "any",
            input: true,
            required: true
        },
        index: {
            label: "Текущий индекс",
            description: "Индекс в таблице",
            type: "integer"
        },
        needResize: {
            label: "Требуется расширение",
            description: "Флаг необходимости изменения размера таблицы",
            type: "boolean"
        }
    },

    blocks: {
        start: {
            type: "start",
            label: "Начало вставки",
            description: "Инициализация процесса добавления элемента",
            next: "checkLoadFactor"
        },
        
        checkLoadFactor: {
            type: "condition",
            label: "Проверка заполненности",
            description: "Проверка необходимости расширения таблицы",
            conditions: [
                {
                    label: "Требуется расширение",
                    description: "Текущая заполненность превышает loadFactor",
                    next: "performResize"
                },
                {
                    label: "Места достаточно",
                    description: "Можно вставлять без расширения",
                    next: "calculateHash"
                }
            ]
        },
        
        performResize: {
            label: "Расширение таблицы",
            description: "Увеличение размера таблицы в 2 раза",
            next: "calculateHash"
        },
        
        findEmptySlot: {
            label: "Поиск свободного слота",
            description: "Цикл поиска пустого места в таблице",
            loop: "slot_search",
            next: "checkSlot"
        },
        
        calculateHash: {
            label: "Вычисление хеша",
            description: "Получение индекса через хеш-функцию",
            next: "findEmptySlot"
        },
        
        checkSlot: {
            type: "condition",
            label: "Проверка слота",
            description: "Проверка состояния текущего слота",
            conditions: [
                {
                    label: "Слот свободен",
                    description: "Можно вставлять элемент",
                    next: "storeElement"
                },
                {
                    label: "Слот занят",
                    description: "Необходимо пробирование",
                    next: "linearProbing"
                }
            ]
        },
        
        linearProbing: {
            label: "Линейное пробирование",
            description: "Переход к следующему слоту",
            next: "findEmptySlot"
        },
        
        storeElement: {
            label: "Сохранение элемента",
            description: "Запись пары ключ-значение в таблицу",
            next: "loop-end"
        },
        
        finish: {
            type: "finish",
            label: "Вставка завершена",
            description: "Элемент успешно добавлен в таблицу"
        }
    },

    invoke: ({ hashTable, key, value }) => {
        // block: 'checkLoadFactor'
        // msg: Проверяем необходимость расширения таблицы
        const needResize = (hashTable._numElements + 1) / hashTable._size > hashTable._config.loadFactor;
        
        if (needResize) {
            // block: 'performResize'
            // msg: Выполняем расширение таблицы в 2 раза
            hashTable._resize(hashTable._size * 2);
        }
        
        // block: 'calculateHash'
        // msg: Генерируем индекс для вставки при помощи хеш-функции
        let index = hashTable.hash(key);

        // block: 'findEmptySlot'
        // msg: Начинаем поиск свободного слота
        // block: 'checkSlot' 
        // msg: Проверяем текущий слот на пустоту
        while (hashTable._table[index] !== null) {
            // block: 'linearProbing'
            // msg: Выполняем линейное пробирование
            index = (index + 1) % hashTable._size
        }
        // block: 'storeElement'
        // msg: Сохраняем элемент в таблицу
        hashTable._table[index] = [key, value];
        hashTable._numElements++;
        // block: 'finish'
        // msg: Элемент успешно добавлен
        return { success: true, index: index };
    }
});

export const hashTableOpenAddressingInsertAnyAny = hashTableOpenAddressingInsert('any', 'any')