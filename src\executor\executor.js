import algorithms from './algorithms'
import { validateByData, create } from './types'
import { cloneContext, cloneContextTo } from './utils/clone'

function findAlgorithm (key) {
  const algorithm = algorithms[key]
  if (!algorithm) {
    throw Error(`Неизвестный алгоритм: ${key}`)
  }
  return algorithm
}

class Executor {
  _history = []
  _index = 0
  _failed = false
  _ctx

  constructor (invoke, inputs, { start } = {}) {
    this._ctx = inputs
    const cloned = cloneContext(inputs)
    // Фиксируем состояние до выполнения
    this._history.push({ ctx: cloneContext(cloned), block: start })
    try {
      const outputs = invoke(cloned, v => {
        this._history.push({ ...v, ctx: cloneContext(v.ctx) })
      })
      // Фиксируем состояние после выполнения
      this._history.push({
        ctx: cloneContext(outputs),
        block: this._history[this._history.length - 1].block
      })
    } catch (e) {
      this._failed = true
    }
  }

  get isFinished () {
    return this._index === this._history.length - 1
  }

  get isFailed () {
    return this._failed
  }

  get isSuccessful () {
    return !this.isFailed
  }

  get messages () {
    return this._history.slice(0, this._index + 1).map(v => v.msg).filter(v => !!v)
  }

  get ctx () {
    return this._ctx
  }

  get inputs () {
    return this._history[0].ctx
  }

  get outputs () {
    return this._history[this._history.length - 1].ctx
  }

  get currentStep () {
    return this._history[this._index]
  }

  get currentStepNumber () {
    return this._index + 1
  }

  get stepsCount () {
    return this._history.length
  }

  get canBackward () {
    return this._index > 0
  }

  get canForward () {
    return this._index < this._history.length - 1
  }

  _moveTo (index) {
    this._index = index
    cloneContextTo(this._ctx, this.currentStep.ctx)
    return { ...this.currentStep, ctx: this._ctx }
  }

  forward () {
    if (this.canForward) {
      return this._moveTo(this._index + 1)
    }
    return null
  }

  backward () {
    if (this.canBackward) {
      return this._moveTo(this._index - 1)
    }
    return null
  }

  toStart () {
    this._moveTo(0)
  }

  finish () {
    this._moveTo(this._history.length - 1)
  }
}

export function createExecutor (key, inputs = {}, config = {}) {
  const { createRequired = false } = config
  const algorithm = findAlgorithm(key)
  if (createRequired) {
    for (const k in algorithm.data) {
      if (inputs[k] === undefined && algorithm.data[k].required) {
        inputs[k] = create(algorithm.data[k].type)
      }
    }
  }
  const parsed = validateByData(inputs, algorithm.data)
  const blocks = algorithm.blocks ?? {}
  const startBlock = Object.keys(blocks).find(k => blocks[k].type === 'start')
  const executor = new Executor(algorithm.invoke, parsed, { start: startBlock })

  return {
    algorithm,
    executor
  }
}

export function execute (key, inputs) {
  const { executor } = createExecutor(key, inputs)
  executor.finish()
  return executor.outputs
}
