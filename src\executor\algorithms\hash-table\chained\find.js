export const hashTableChainedFind = (keyType, valueType) => ({
    key: "hash-table-chained-find",
    label: "Поиск в Chained Hash Table",
    description: "Поиск значения в хеш-таблице с использованием цепочек",
    complexity: {
        time: {
            worst: "O(n)",
            avg: "O(1)",
        best: "O(1)"
        }
    },
    initial: "start",
  
    data: {
        hashTable: {
        label: "HashTableChained",
        description: "Хеш-таблица на открытых цепочках",
        type: "hash-table-chained",
        input: true,
        required: true
        },

        key: {
            label: "Ключ",
            description: "Ключ для поиска",
            type: "any",
            input: true,
            required: true
        },
        
        value: {
            label: "Найденное значение",
            description: "Результат поиска",
            type: "any",
            output: true
        },

        index: {
            label: "Индекс корзины",
            description: "Индекс, вычисленный хеш-функцией",
            type: "integer"
        }
    },

    blocks: {
        start: {
            type: "start",
            label: "Начало поиска",
            description: "Инициализация процесса поиска элемента",
            next: "calculateIndex"
        },
        
        calculateIndex: {
            type: "action",
            label: "Вычисление индекса",
            description: "Получаем индекс корзины через хеш-функцию",
            next: "searchInBucket"
        },
        
        searchInBucket: {
            type: "action",
            label: "Поиск в корзине",
            description: "Перебираем элементы в найденной корзине",
            next: "checkFound"
        },
        
        checkFound: {
            type: "condition",
            label: "Проверка результата",
            description: "Определяем, найден ли элемент",
            conditions: [
                {
                    label: "Найден",
                    description: "Элемент существует в корзине",
                    next: "returnValue"
                },
                {
                    label: "Не найден",
                    description: "Элемент отсутствует в таблице",
                    next: "returnUndefined"
                }
            ]
        },
        
        returnValue: {
            type: "action",
            label: "Возврат значения",
            description: "Возвращаем найденное значение",
            next: "finish"
        },
        
        returnUndefined: {
            type: "action",
            label: "Элемент не найден",
            description: "Возвращаем undefined",
            next: "finish"
        },
        
        finish: {
            type: "finish",
            label: "Поиск завершен",
            description: "Операция поиска завершена"
        }
    },

    invoke: ({ hashTable, key }) => {
        // block: 'calculateIndex'
        // msg: Вычисляем индекс корзины с помощью хеш-функции
        const index = hashTable.hash(key);
    
        const bucket = hashTable._table[index];
        
        // block: 'searchInBucket'
        // msg: Перебираем элементы в корзине для поиска совпадения по ключу
        for (const pair of bucket) {
            // block: 'checkFound'
            // msg: Сверяем ключи объектов
            if (pair.key === key) {
                // block: 'returnValue'
                // msg: Ключ найден, возвращаем соответствующее значение
                return { value: pair.value };
            }
        }
        
        // block: 'returnUndefined'
        // msg: Ключ не найден в таблице
        return { value: undefined };
    }
});

export const hashTableChainedAnyAny = hashTableChainedFind('any', 'any')