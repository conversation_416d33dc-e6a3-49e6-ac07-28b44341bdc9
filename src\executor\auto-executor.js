class AutoExecutor {
  _interval = 1000
  _timer
  _executor

  constructor (executor, config = {}) {
    this._executor = executor
    this._interval = config.interval ?? this._interval
  }

  get isRunning () {
    return !!this._timer
  }

  start () {
    if (!this._timer) {
      this._timer = setInterval(() => {
        if (this._executor.canForward) {
          this._executor.forward()
        } else {
          this.pause()
        }
      }, this._interval)
    }
  }

  pause () {
    if (this._timer) {
      clearInterval(this._timer)
      this._timer = null
    }
  }

  finish () {
    this.pause()
    this._executor.finish()
  }
}

export function createAutoExecutor (executor, config = {}) {
  return new AutoExecutor(executor, config)
}
