<template>
  <Combobox by="label">
    <ComboboxAnchor class="w-full">
      <div class="relative w-full items-center">
        <ComboboxInput
          class="pl-9"
          :display-value="(val) => val?.label ?? ''"
          placeholder="Поиск алгоритма..."
        />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
          <Search class="size-4 text-muted-foreground" />
        </span>
      </div>
    </ComboboxAnchor>

    <ComboboxList>
      <ComboboxEmpty>
        Ничего не найдено.
      </ComboboxEmpty>

      <ComboboxGroup>
        <ComboboxItem
          v-for="item in results"
          :key="item.key"
          :value="item.key"
          @click="$emit('select', item)"
        >
          {{ item.label }}

          <ComboboxItemIndicator>
            <Check :class="cn('ml-auto h-4 w-4')" />
          </ComboboxItemIndicator>
        </ComboboxItem>
      </ComboboxGroup>
    </ComboboxList>
  </Combobox>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { useDebounceFn } from '@vueuse/core'

  import { algorithmOptions } from '@/executor'
  import { Search, Check } from 'lucide-vue-next'
  import { cn } from '@/lib/utils'
  import { Combobox, ComboboxAnchor, ComboboxEmpty, ComboboxGroup, ComboboxInput, ComboboxItem, ComboboxItemIndicator, ComboboxList } from '@/components/ui/combobox'

  defineEmits(['select'])

  const query = ref('')
  const results = ref(algorithmOptions)

  function search (v) {
    v = v.trim().toLowerCase()
    results.value = algorithmOptions.filter(
      a => a.label.toLowerCase().includes(v)
    )
  }

  const debouncedSearch = useDebounceFn(search, 300)

  watch(query, debouncedSearch)
</script>
