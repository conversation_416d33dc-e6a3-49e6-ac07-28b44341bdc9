import { z } from 'zod'

function applyAtributes (schema, type) {
  if (!type.attrs?.length) {
    return schema
  }
  if (type.attrs.includes('nonzero')) {
    schema = schema.not(z.literal(0))
  } else if (type.attrs.includes('positive')) {
    schema = schema.min(1)
  } else if (type.attrs.includes('nonnegative')) {
    schema = schema.min(0)
  }
  return schema
}

export function toNumberSchema (type) {
  return applyAtributes(z.number(), type)
}

export function toIntegerSchema (type) {
  return applyAtributes(z.number().int(), type)
}

const MAX = 500
const MIN = 1

function minLimit (type) {
  if (type.attrs?.includes('nonzero')) {
    return 1
  }
  if (type.attrs?.includes('positive')) {
    return 1
  }
  if (type.attrs?.includes('nonnegative')) {
    return 0
  }
  return MIN
}

export function createNumber (type) {
  const min = minLimit(type)
  return Math.round((Math.random() * (MAX - min + 1) + min) * 100) / 100
}

export function createInteger (type) {
  const min = minLimit(type)
  return Math.floor(Math.random() * (MAX - min + 1)) + min
}

export function parseNumber (value) {
  return Number(value)
}

export function parseInteger (value) {
  return Math.floor(Number(value))
}

export function toParsingRegexp (type, { toRegexp }) {
}