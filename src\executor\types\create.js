import { parseTypeString } from './type-parser.js'
import { createPair } from './pair/index.js'
import { createString, createChar } from './string/index.js'
import { createNumber, createInteger } from './number/index.js'
import { createArray } from './array/index.js'
import { createGraph } from './graph/index.js'
import { createBst } from './tree/index.js'
import { createHashTable } from './hash-table/create.js'

const factory = {
  pair: createPair,
  string: createString,
  char: createChar,
  number: createNumber,
  integer: createInteger,
  array: createArray,
  graph: createGraph,
  bst: createBst,
  any: createChar,
  hash: createHashTable,
  boolean: () => Math.random() < 0.5
}

export function create (type) {
  type = parseTypeString(type)
  const generator = factory[type.name]
  if (!generator) {
    throw new Error(`Неизвестный тип ${type.name}`)
  }
  return generator(type, { create, parseType: parseTypeString })
}