import { parseTypeString } from './type-parser'
import { createPair } from './pair'
import { createString, createChar } from './string'
import { createNumber, createInteger } from './number'
import { createArray } from './array'
import { createGraph } from './graph'
import { createBst } from './tree'
import { createHashTable } from './hash-table/create.js'

const factory = {
  pair: createPair,
  string: createString,
  char: createChar,
  number: createNumber,
  integer: createInteger,
  array: createArray,
  graph: createGraph,
  bst: createBst,
  any: createChar,
  hash: createHashTable,
  boolean: () => Math.random() < 0.5
}

export function create (type) {
  type = parseTypeString(type)
  const generator = factory[type.name]
  if (!generator) {
    throw new Error(`Неизвестный тип ${type.name}`)
  }
  return generator(type, { create, parseType: parseTypeString })
}