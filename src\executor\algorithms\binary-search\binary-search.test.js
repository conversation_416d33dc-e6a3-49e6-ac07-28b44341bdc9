import { expect, test } from 'vitest'

import { binarySearchNumber } from '.'
import { createExecutor } from '../../executor'

test('binary search to work properly', () => {
  let result = null
  const array = [2, 4, 7, 9, 12, 15, 19, 23, 26, 30, 31, 33, 35, 37, 42, 43, 44, 50, 52, 55, 65, 72, 89, 101]
  for (let i = 0; i < array.length; i++) {
    result = binarySearchNumber.invoke({ array, needle: array[i] })
    expect(result.index).toBe(i)
  }

  result = binarySearchNumber.invoke({ array, needle: 20 })
  expect(result.index).toBe(-1)

  result = binarySearchNumber.invoke({ array, needle: 110 })
  expect(result.index).toBe(-1)
})

test('binary search validates array sortedness', () => {
  createExecutor(
    'binary-search-number',
    { array: [2,3,4], needle: 3 }
  )
  expect(() => createExecutor(
    'binary-search-number',
    { array: [2,4,3], needle: 3 }
  )).toThrowError()
})