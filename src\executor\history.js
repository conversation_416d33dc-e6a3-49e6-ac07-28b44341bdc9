import { cloneContext, cloneContextTo } from './utils/clone'

class ExecutionHistory {
  _history = []
  _redoHistory = []
  _meta
  _ctx

  get ctx () {
    return this._ctx
  }

  get meta () {
    return this._meta
  }

  get size () {
    return this._history.length
  }

  get canUndo () {
    return this._history.length > 0
  }

  get canRedo () {
    return this._redoHistory.length > 0
  }

  undo () {
    if (this.canUndo) {
      this._redoHistory.push({
        ctx: cloneContext(this._ctx),
        meta: this._meta
      })
      const { meta, ctx } = this._history.pop()
      cloneContextTo(this._ctx, ctx)
      this._meta = meta
    }
  }

  redo () {
    if (this.canRedo) {
      this._history.push({
        ctx: cloneContext(this._ctx),
        meta: this._meta
      })
      const { meta, ctx } = this._redoHistory.pop()
      cloneContextTo(this._ctx, ctx)
      this._meta = meta
    }
  }

  add (ctx, meta) {
    if (this._ctx) {
      this._history.push({
        ctx: cloneContext(this._ctx),
        meta: this._meta
      })
    }
    this._ctx = ctx
    this._meta = meta
    this._redoHistory = []
  }
}

export function createExecutionHistory (initialCtx) {
  const history = new ExecutionHistory()
  if (initialCtx) {
    history.add(initialCtx, null)
  }
  return history
}
