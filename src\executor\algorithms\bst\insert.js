import { bstFind } from './find'

export const bstInsert = (keyType, valueType) => ({
	key: `bst-${keyType}-${valueType}_insert`,
	label: `Вставка в BST (${keyType}, ${valueType})`,
	description: 'Вставка значения в бинарное дерево поиска по ключу',
	complexity: {
		time: 'O(log n)'
	},
	initial: 'start',
	dependencies: [`bst-${keyType}-${valueType}:find`],

	data: {
		tree: {
			label: 'BST',
			description: 'Бинарное дерево поиска',
			type: `bst<${keyType},${valueType}>`,
			input: true,
			required: true,
			output: true
		},
		key: {
			label: 'Ключ',
			type: keyType,
			input: true,
			required: true
		},
		value: {
			label: 'Значение',
			description: 'Значение, которое нужно вставить в дерево',
			type: valueType,
		}
	},

	blocks: {
		...bstFind(keyType, valueType).blocks
	},

	validations: [],

	invoke ({ tree, key, value }) {
		const { node, parent, left } = bstFind(keyType, valueType).invoke({ tree, key })

		if (node) {
			// msg: 'Узел с таким ключом уже существует, перезаписываем значение'
			node.value = value
		} else {
			// msg: 'Узла с таким ключом нет, добавляем новый узел'
			if (parent) {
				if (left) {
					tree.addLeftChild({ key, value }, parent)
				} else {
					tree.addRightChild({ key, value }, parent)
				}
				// msg: 'Добавляем узел в качестве ребёнка родительского узла'
			} else {
				tree.addNode({ key, value })
				// msg: 'Дерево пустое, добавляем корень'
			}
		}
	}
})

export const bstInsertStringString = bstInsert('string', 'string')
export const bstInsertStringNumber = bstInsert('string', 'number')
export const bstInsertNumberString = bstInsert('number', 'string')
export const bstInsertNumberNumber = bstInsert('number', 'number')
