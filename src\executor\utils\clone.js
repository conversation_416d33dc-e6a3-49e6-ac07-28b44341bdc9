export function clone (v) {
  if (v === null) {
    return null
  }
  const type = typeof v
  const isArray  = Array.isArray(v)
  const isString = (type === 'string')
  const isObject = !isArray && (type === 'object')

  if (isString) {
    return v.slice()
  }

  if (isArray) {
    return v.map(x => clone(x))
  }

  // Примитивный тип
  if (!isObject) {
    return v
  }

  if (isObject && (typeof v.clone === 'function')) {
    return v.clone()
  }

  if (isObject) {
    const res = {}
    for (const k in v) {
      res[k] = clone(v[k])
    }
    return res
  }
}

export function cloneContext (ctx) {
  const res = {}
  for (const k in ctx) {
    res[k] = clone(ctx[k])
  }
  return res
}

/* TODO: Глубокая проверка равенства перед клонированием. Позволит
  избежать лишних операций и перерендеринга при реактивности.
  */
export function cloneContextTo (to, from) {
  for (const k in from) {
    to[k] = clone(from[k])
  }
  for (const k in to) {
    if (from[k] === undefined) {
      delete to[k]
    }
  }
}
