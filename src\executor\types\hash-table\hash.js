import { murmurHash3 } from "./hash-function/murmurhash3.js"
import { array } from "zod"
import { table } from "console"
import { linearProbing, quadraticProbing, doubleHashing } from "./proding-function/functions.js"

export class HashTable {
    _size = 0
    _config = {}
    _table = []

    constructor (config = {}) {
        this._config = {
            size : 10,
            loadFactor : 0.75,
            ...config
        }
        this._initializeTable(this._config.size)
    }

    _initializeTable(size) {
        this._size = size
        this._table = new Array(size).fill(null)
    }

    hash(key) {
        const keyString = typeof key === 'string' ? key : JSON.stringify(key);
        return (keyString, 0) % this._size;
    }

    clone() {
        return new HashTable()
    }
}

export class HashTableOpenAddressing extends HashTable {
    _numElements = 0;
     
    _resize(newSize) {
        const oldTable = this._table
        this._initializeTable(newSize)

        this._size = newSize

        for (let i = 0; i < oldTable.length; i++) {
            const item = oldTable[i]
            if (item !== null) {
                this._table[i] = item
            }
        }
    }

    set(key, value) {
       if ((this._numElements + 1) / this._size > this._config.loadFactor) {
            this._resize(this._size * 2)
       }

       let index = this.hash(key)
       while (this._table[index] !== null) {
            index = linearProbing(index, this._size)
       }
       this._numElements = this._numElements + 1
       this._table[index] = [key, value]
    }

    get(key) {
        let index = this.hash(key)
        while (this._table[index] !== null) {
            if (this._table[index][0] === key) {
                return this._table[index][1]
            }
            index = linearProbing(index, this._size)
        }
        return null
    }
}

export class HashTableChained extends HashTable {

    _numElements = 0;

    _initializeTable(size) {
        this._size = size
        this._table = new Array(size).fill(null).map(() => new Array())
    }
    
    _resize(newSize) {
        const oldChains = this._table.flat();
        this._initializeTable(newSize);
        
        for (const {key, value} of oldChains) {
            this.set(key, value);
        }
    }

   set(key, value) {
        if ((this._numElements + 1) / this._size > this._config.loadFactor) {
            this._resize(this._size * 2)
        }

        const index = this.hash(key)

        if (!this._table[index]) {
            this._table[index] = []
        }

        this._table[index].push({key, value})
        this._numElements++
    }

    get(key) {
        const index = this.hash(key)
        const list = this._table[index]
        
        if (!list) return undefined

        for (const pair of list) {
            if (pair.key === key) {
                return pair.value
            }
        }
        return undefined
    }s

}