# Платформа визуализации алгоритмов и структур данных

Адрес сайта - [dsalgo.ru](https://dsalgo.ru)

Сервис реализует наглядные визуализаторы основных принципов работы структур данных и алгоритмов. Предназначено для студентов IT-специальностей, а также всех интересующихся.

Проект изначально создан как учебный материал для курса «Структуры и алгоритмы компьютерной обработки данных», преподаваемого на 2 курсе направления «Прикладная информатика» факультета компьютерных наук Воронежского государственного университета.

Основной компонент сервиса - пошаговый симулятор выполнения алгоритмов. [Документация по нему](src/executor/README.md)

## Стек

- Vue 3
- JS / TypeScript
- Vite
- [Vitest](https://vitest.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn UI](https://www.shadcn-vue.com/) (библиотека компонентов)
- [Mermaid](https://mermaid.js.org/) (для отрисовки блок-схем)

## Запуск проекта

Установка зависимостей:
```
npm install
```

Запуск в режиме разработки:
```
npm run dev
```

Запуск тестов:
```
npm run test
```
