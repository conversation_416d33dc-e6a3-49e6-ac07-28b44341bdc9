import MainLayout from "./layouts/MainLayout.vue"

const routes = [
    {
        path: '/',
        component: () => import('./pages/ConstructionPage.vue')
    },
    // {
    //     name: '/home',
    //     path: '',
    //     component: () => import('./pages/HomePage.vue')
    // },
    {
        component: MainLayout,
        path: '/test',
        children: [
            {
                name: 'test',
                path: '',
                component: () => import('./pages/TestPage.vue')
            },
            {
                name: 'test-fields',
                path: 'fields',
                component: () => import('./pages/TestFieldsPage.vue')
            },
            {
                name: 'graph-test',
                path: 'graph',
                component: () => import('./pages/TestGraphPage.vue')
            },
            {
                name: 'tree-test',
                path: 'tree',
                component: () => import('./pages/TestTreePage.vue')
            }
        ]
    }
]

export default routes