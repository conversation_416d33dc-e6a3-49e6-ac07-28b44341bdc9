<template>
  <FormField
    v-slot="{ componentField, setValue, resetField }"
    :name="name"
  >
    <FormItem>
      <FormLabel>
        {{ config.label }} {{ config.required ? '*' : '' }}
      </FormLabel>

      <FormControl>
        <div class="flex gap-2">
          <Input
            v-if="inputType"
            v-bind="componentField"
            :type="inputType"
          />

          <Input
            v-if="!inputType"
            :model-value="componentField.modelValue ? String(componentField.modelValue): ''"
            readonly
          />

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant="outline"
                  size="icon"
                  class="cursor-pointer"
                  @click.prevent="setValue(create(config.type))"
                >
                  <Dices />
                </Button>
              </TooltipTrigger>

              <TooltipContent>
                <p>Сгенерировать случайно</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant="outline"
                  size="icon"
                  class="cursor-pointer"
                  @click.prevent="resetField"
                >
                  <RefreshCw />
                </Button>
              </TooltipTrigger>

              <TooltipContent>
                <p>Сбросить значение</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </FormControl>

      <FormDescription v-if="config.description">
        {{ config.description }}
      </FormDescription>

      <FormMessage  />
    </FormItem>
  </FormField>
</template>

<script setup>
  import { computed } from 'vue'
  import { Dices, RefreshCw } from 'lucide-vue-next'

  import { create } from '@/executor'
  import { useAlgorithmFormItemProps } from './item'

  import {
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
  } from '@/components/ui/form'
  import { Input } from '@/components/ui/input'
  import { Button } from '@/components/ui/button'
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
  } from '@/components/ui/tooltip'

  const props = defineProps(useAlgorithmFormItemProps)
  
  const INPUT_TYPES = {
    integer: 'number',
    number: 'number',
    string: 'text',
    char: 'text'
  }

  const inputType = computed(
    () => INPUT_TYPES[props.config.type] ?? null
  )
</script>