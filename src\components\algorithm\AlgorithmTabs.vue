<template>
  <Tabs default-value="info" class="width-full">
    <div class="px-5">
      <TabsList class="grid w-full grid-cols-3">
        <TabsTrigger value="info" class="cursor-pointer">
          <Info /><span class="ml-1 md:hidden lg:block">Описание</span>
        </TabsTrigger>

        <TabsTrigger value="flowchart" class="cursor-pointer">
          <Workflow /><span class="ml-1 md:hidden lg:block">Блок-схема</span>
        </TabsTrigger>

        <TabsTrigger value="code" class="cursor-pointer">
          <Code /><span class="ml-1 md:hidden lg:block">Код</span>
        </TabsTrigger>
      </TabsList>
    </div>

    <TabsContent value="info" class="px-5 py-4">
      <AlgorithmInfo
        :algorithm="algorithm"
      />
    </TabsContent>

    <TabsContent value="flowchart" class="p-1">
      <AlgorithmFlowchart
        :blocks="algorithm.blocks"
        :current="block"
      />
    </TabsContent>

    <TabsContent value="code" class="px-5 py-2">
      <AlgorithmCode
        :code="algorithm.code"
        :highlight="block"
      />
    </TabsContent>
  </Tabs>
</template>

<script setup>
  import { Code, Info, Workflow } from 'lucide-vue-next'

  import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger
  } from '@/components/ui/tabs'

  import { AlgorithmInfo, AlgorithmFlowchart, AlgorithmCode } from './blocks'

  defineProps({
    algorithm: Object,
    block: String
  })
</script>