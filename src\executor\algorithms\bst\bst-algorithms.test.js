import { expect, test } from 'vitest'

import { BinarySearchTree } from '../../types'
import { bstInsertNumberNumber, bstFindNumberNumber } from '.'

test('BST insert and search', () => {
  const tree = new BinarySearchTree({ keyType: 'number' })
  bstInsertNumberNumber.invoke({ tree, key: 6, value: 6 })
  bstInsertNumberNumber.invoke({ tree, key: 9, value: 9 })
  bstInsertNumberNumber.invoke({ tree, key: 4, value: 4 })
  bstInsertNumberNumber.invoke({ tree, key: 7, value: 7 })
  bstInsertNumberNumber.invoke({ tree, key: 11, value: 11 })
  bstInsertNumberNumber.invoke({ tree, key: 3, value: 3 })

  const root = tree.root
  const leftChild = tree.getLeftNode(root)
  const rightChild = tree.getRightNode(root)
  const leftLeftChild = tree.getLeftNode(leftChild)
  const leftRightChild = tree.getRightNode(leftChild)
  const rightRightChild = tree.getRightNode(rightChild)
  const rightLeftChild = tree.getLeftNode(rightChild)
  expect(root.key).toBe(6)
  expect(root.value).toBe(6)
  expect(leftChild.key).toBe(4)
  expect(leftChild.value).toBe(4)
  expect(rightChild.key).toBe(9)
  expect(rightChild.value).toBe(9)
  expect(leftLeftChild.key).toBe(3)
  expect(leftLeftChild.value).toBe(3)
  expect(rightLeftChild.key).toBe(7)
  expect(rightLeftChild.value).toBe(7)
  expect(rightRightChild.key).toBe(11)
  expect(rightRightChild.value).toBe(11)
  expect(leftRightChild).toBeNull()

  expect(bstFindNumberNumber.invoke({ tree, key: 6 }).value).toBe(6)
  expect(bstFindNumberNumber.invoke({ tree, key: 9 }).value).toBe(9)
  expect(bstFindNumberNumber.invoke({ tree, key: 4 }).value).toBe(4)
  expect(bstFindNumberNumber.invoke({ tree, key: 7 }).value).toBe(7)
  expect(bstFindNumberNumber.invoke({ tree, key: 11 }).value).toBe(11)
  expect(bstFindNumberNumber.invoke({ tree, key: 3 }).value).toBe(3)
  expect(bstFindNumberNumber.invoke({ tree, key: 1 }).value).toBeNull()
  expect(bstFindNumberNumber.invoke({ tree, key: 10 }).value).toBeNull()
})