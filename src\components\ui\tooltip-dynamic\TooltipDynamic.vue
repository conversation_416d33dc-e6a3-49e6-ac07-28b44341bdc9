<script setup>
  import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
  import {
    useFloating,
    autoUpdate,
    offset,
    shift,
    flip
  } from '@floating-ui/vue'
  import { cn } from '@/lib/utils'

  const props = defineProps({
    class: {
      type: String,
      required: true
    },
    content: {
      type: [String, Function]
    },
    selector: {
      type: String,
      required: true
    },
    active: {
      type: Boolean,
      default: true
    }
  })

  const isOpen = ref(false)
  const containerRef = ref(null)
  const tooltipRef = ref(null)
  const currentElement = ref(null)
  const computedContent = ref('')

  let elements = []
  let autoUpdateCleanup = null

  const showTooltip = computed(() => props.active && isOpen.value && computedContent.value)

  const { floatingStyles, update } = useFloating(
    currentElement, tooltipRef,
    {
      placement: 'top',
      middleware: [offset(8), shift(), flip()]
    }
  )

  watch(
    [() => currentElement.value, () => tooltipRef.value],
    ([reference, floating]) => {
      // Очищаем предыдущее автообновление
      if (autoUpdateCleanup) {
        autoUpdateCleanup()
        autoUpdateCleanup = null
      }
      
      // Запускаем автообновление при наличии элементов
      if (reference && floating) {
        autoUpdateCleanup = autoUpdate(reference, floating, update)
      }
    },
    { flush: 'post' }
  )

  watch(computedContent, () => {
    if (showTooltip.value) update()
  })

  const handleMouseEnter = (event) => {
    if (!props.active) return
    currentElement.value = event.currentTarget
    
    // Обновляем контент
    computedContent.value = typeof props.content === 'function'
      ? props.content(event.currentTarget)
      : props.content
      
    isOpen.value = true
  }

  const handleMouseLeave = () => {
    isOpen.value = false
    // Не сбрасываем currentElement сразу (нужен для анимации закрытия)
  }

  const attachEvents = () => {
    if (containerRef.value) {
      elements = Array.from(
        containerRef.value.querySelectorAll(props.selector)
      )
      
      elements.forEach(el => {
        el.addEventListener('mouseenter', handleMouseEnter)
        el.addEventListener('mouseleave', handleMouseLeave)
      })
    }
  }

  const detachEvents = () => {
    elements.forEach(el => {
      el.removeEventListener('mouseenter', handleMouseEnter)
      el.removeEventListener('mouseleave', handleMouseLeave)
    })
    elements = []
  }

  const reattachEvents = () => {
    detachEvents()
    attachEvents()
  }

  watch(() => props.selector, reattachEvents)
  watch(() => props.active, (v) => v ? reattachEvents() : detachEvents())

  onMounted(attachEvents)
  onBeforeUnmount(() => {
    detachEvents()
    if (autoUpdateCleanup) autoUpdateCleanup()
  })
</script>

<template>
  <div ref="containerRef">
    <slot></slot>
  </div>

  <div
    ref="tooltipRef"
    :style="floatingStyles"
    :class="
      cn(
        'bg-primary text-primary-foreground z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance',
        props.class,
        {
          'invisible opacity-0': !showTooltip
        }
      )
    "
  >
    {{ computedContent }}
  </div>
</template>