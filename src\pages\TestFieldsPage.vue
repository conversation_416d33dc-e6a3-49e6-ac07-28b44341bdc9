<template>
  <div class="p-10">
    <AlgorithmForm
      :data="data"
      :initial="{ testInteger: 10 }"
      create-required
    />
  </div>
</template>

<script setup>
  import { AlgorithmForm } from '@/components/algorithm/form'

  const data = {
    testInteger: {
      label: 'Тест (integer)',
      description: 'Описание входного параметра',
      type: 'integer',
      required: true
    },
    testNumber: {
      label: 'Тест (number)',
      description: 'Описание входного параметра',
      type: 'number',
      required: true,
      default: 10.5
    },
    testString: {
      label: 'Тест (string)',
      description: 'Описание входного параметра',
      type: 'string',
      required: true,
      default: '10'
    },
    testChar: {
      label: 'Тест (char)',
      description: 'Описание входного параметра',
      type: 'char',
      required: true
    },
    testBoolean: {
      label: 'Тест (boolean)',
      description: 'Описание входного параметра',
      type: 'boolean',
      default: true
    },
    testArray: {
      label: 'Тест (array)',
      description: 'Описание входного параметра',
      type: 'array',
      required: true
    },
    testBst: {
      label: 'Тест (bst)',
      description: 'Описание входного параметра',
      type: 'bst'
    }
  }
</script>