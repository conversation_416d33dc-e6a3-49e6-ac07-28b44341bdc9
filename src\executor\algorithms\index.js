import { binarySearchN<PERSON>ber, binarySearchString } from "./binary-search"
import { exponentialSearchNumber, exponentialSearchString } from './exponential-search'
import { insertionSortNumber, insertionSortString } from './insertion-sort'
import { bstFind, bstInsert } from './bst'

const algorithms = {
  [binarySearchNumber.key]: binarySearchNumber,
  [binarySearchString.key]: binarySearchString,
  [exponentialSearchNumber.key]: exponentialSearchNumber,
  [exponentialSearchString.key]: exponentialSearchString,
  [insertionSortNumber.key]: insertionSortNumber,
  [insertionSortString.key]: insertionSortString,
  [bstFind.key]: bstFind,
  [bstInsert.key]: bstInsert
  
}

export const options = Object.values(algorithms)
  .map(v => ({ key: v.key, label: v.label }))

export default algorithms