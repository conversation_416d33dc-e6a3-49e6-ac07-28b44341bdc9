class TypeParser {
  constructor (input) {
    this.input = input
    this.pos = 0
  }

  parse () {
    const result = this.parseType()
    this.skipWhitespace()
    if (this.pos < this.input.length) {
      throw new Error(`Неожиданный токен в позиции ${this.pos}: '${this.input[this.pos]}'`)
    }
    return result
  }

  parseType () {
    this.skipWhitespace()
    const name = this.parseIdentifier()

    // парсинг элементов <...>
    let items
    if (this.peek() === '<') {
      items = this.parseItems()
    }

    // парсинг подтипов (...)
    let sub
    if (this.peek() === '(') {
      sub = this.parseSubtypes()
    }

    // парсинг атрибутов
    let attrs
    while (this.peek() === '.') {
      attrs = attrs || []
      attrs.push(this.parseAttribute())
    }

    const typeObj = { name }
    if (items) {
      typeObj.items = items
    }
    if (sub) {
      typeObj.sub = sub
    }
    if (attrs) {
      typeObj.attrs = attrs
    }
    return typeObj
  }

  parseIdentifier () {
    this.skipWhitespace()
    const start = this.pos
    while (/[a-zA-Z]/.test(this.peek())) {
      this.pos++
    }
    if (this.pos === start) {
      throw new Error(`Ожидался идентификатор в позиции ${start}`)
    }
    return this.input.slice(start, this.pos)
  }

  parseItems () {
    this.expect('<')
    const items = []
    do {
      this.skipWhitespace()
      const type = this.parseType()
      items.push(type)
      this.skipWhitespace()
    } while (this.eat(','))
    this.expect('>')
    return items
  }

  parseSubtypes () {
    this.expect('(')
    const subs = []
    do {
      this.skipWhitespace()
      const subType = this.parseType()
      subs.push(subType)
      this.skipWhitespace()
    } while (this.eat(','))
    this.expect(')')
    return subs
  }

  parseAttribute () {
    this.expect('.')
    const attr = this.parseIdentifier()
    return attr
  }

  skipWhitespace () {
    while (this.peek() === ' ') {
      this.pos++
    }
  }

  peek () {
    return this.input[this.pos] || ''
  }

  eat (ch) {
    if (this.peek() === ch) {
      this.pos++
      return true
    }
    return false
  }

  expect (ch) {
    if (this.peek() !== ch) {
      throw new Error(`Ожидался символ '${ch}' в позиции ${this.pos}, найден '${this.peek()}'`)
    }
    this.pos++
  }
}

export function parseTypeString (typeStr) {
  if (typeof typeStr !== 'string') {
    return typeStr
  }
  const parser = new TypeParser(typeStr)
  return parser.parse()
}
