import { z } from 'zod'

export function hasAttribute (type, attr) {
  return type.attrs?.includes(attr)
}

export function hasSubtype (type, name, parse) {
  return type.sub?.map(parse).find(t => t.name === name)
}

export function instanceSchema (klass, fieldSchemas) {
  let res = z.custom()
    .refine(v => v instanceof klass, {
      message: "Не является экземпляром нужного класса"
    })
  if (Object.values(fieldSchemas).filter(v => !!v)) {
    res = res.superRefine((val, ctx) => {
      for (const k in fieldSchemas) {
        const schema = fieldSchemas[k]
        const result = schema.safeParse(val[k])
        if (!result.success) {
          result.error.issues.forEach(issue => {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `${k}: ${issue.message}`,
            })
          })
        }
      }
    })
  }
  return res
}

const openBrackets = '([{'
const closeBrackets = ')]}'

export function trimBraces (str) {
  if (openBrackets.includes(str[0]) && closeBrackets.includes(str[str.length - 1])) {
    return str.slice(1, -1)
  }
  return str
}

export function splitTopLevel (str) {
  const result = []
  let current = ''
  let level = 0

  for (let i = 0; i < str.length; i++) {
    const char = str[i]

    if (openBrackets.includes(char)) {
      level++
    } else if (closeBrackets.includes(char)) {
      level--
    }

    if (char === ',' && level === 0) {
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }

  if (current) {
    result.push(current.trim())
  }

  return result
}