import { Graph } from './graph'
import { hasSubtype } from '../utils'

export function createGraph (type, { create, parseType }) {
  const nodeType = type.items?.length ? type.items[0] : 'any'
  const directed = hasSubtype(type, 'directed', parseType)
  const weighted = hasSubtype(type, 'weighted', parseType)
  const graph = new Graph({ directed: !!directed })
  const nodesCount = 10
  for (let i = 0; i < nodesCount; i++) {
    graph.addNode(create(nodeType))
  }
  const linksCount = weighted ? 30 : 15
  const linkType = weighted ? weighted.items[0] : 'integer'
  for (let i = 0; i < linksCount; i++) {
    const node1 = graph.nodes[Math.floor(Math.random() * nodesCount)]
    const node2 = graph.nodes[Math.floor(Math.random() * nodesCount)]
    if (node1 === node2 || graph.isLinked(node1, node2)) {
      continue
    }
    graph.addLink(node1, node2, linkType ? create(linkType) : null)
  }
  return graph
}