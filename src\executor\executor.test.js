import { expect, test } from 'vitest'

import { createExecutor } from './executor'

test('executor history', () => {
  const array = [2, 4, 7, 9, 12, 15, 19, 23, 26, 30, 31, 33, 35, 37, 42, 43, 44, 50, 52, 55, 65, 72, 89, 101]
  const ctx = {
    array,
    needle: 12
  }
  const { executor } = createExecutor('binary-search-number', ctx)
  executor.forward()
  executor.forward()
  executor.forward()
  executor.forward()
  expect(executor.ctx.right).toBe(23)
  executor.forward()
  expect(executor.ctx.right).toBe(10)
  expect(executor.canBackward).toBe(true)

  executor.backward()
  expect(executor.ctx.right).toBe(23)
  executor.backward()
  executor.backward()
  executor.backward()
  executor.backward()
  expect(executor.ctx.right).toBe(undefined)
  expect(executor.canBackward).toBe(false)
  expect(executor.isFinished).toBe(false)
  
  executor.forward()
  executor.forward()
  executor.forward()
  executor.forward()
  expect(executor.ctx.right).toBe(23)
  executor.forward()
  expect(executor.ctx.right).toBe(10)
  expect(executor.isFinished).toBe(false)
})