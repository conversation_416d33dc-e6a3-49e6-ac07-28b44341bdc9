<template>
  <AlgorithmFormCheckbox
    v-if="showCheckbox"
    :config="config"
    :name="name"
  />

  <AlgorithmFormInput
    v-if="showInput"
    :config="config"
    :name="name"
  />
</template>

<script setup>
  import { computed } from 'vue'
  
  import { useAlgorithmFormItemProps } from './item'

  import AlgorithmFormCheckbox from './AlgorithmFormCheckbox.vue'
  import AlgorithmFormInput from './AlgorithmFormInput.vue'

  const props = defineProps(useAlgorithmFormItemProps)

  const showCheckbox = computed(() => props.config.type === 'boolean')
  const showInput = computed(() => !showCheckbox.value)
</script>