<template>
  <Table>
    <TableHeader v-if="showIndexes && (is2D || !vertical)">
      <TableRow>
        <TableHead v-if="is2D"></TableHead>
        <TableHead
          v-for="(_, i) in (is2D ? array[0] : array)"
          class="opacity-40 text-center"
        >
          {{ i }}
        </TableHead>
      </TableRow>
    </TableHeader>

    <TableBody>
      <template v-if="is2D">
        <TableRow v-for="(row, i) in (vertical ? array[0] : array)">
          <TableHead
            v-if="showIndexes"
            class="opacity-40 text-center"
          >
            {{ i }}
          </TableHead>
          
          <ArrayVisualizerCell
            v-for="(_, j) in (vertical ? array : row)"
            :cell="vertical ? array[j][i] : array[i][j]"
            :pos="vertical ? [j, i] : [i, j]"
            v-bind="cellProps"
          />
        </TableRow>
      </template>

      <template v-else>
        <template v-if="vertical">
          <TableRow v-for="(cell, i) in array">
            <TableHead
              v-if="showIndexes"
              class="opacity-40 text-center"
            >
              {{ i }}
            </TableHead>
            
            <ArrayVisualizerCell
              :cell="cell"
              :pos="i"
              v-bind="cellProps"
            />
          </TableRow>
        </template>

        <TableRow v-else>
          <ArrayVisualizerCell
            v-for="(cell, i) in array"
            :cell="cell"
            :pos="i"
            v-bind="cellProps"
          />
        </TableRow>
      </template>
    </TableBody>
  </Table>
</template>

<script setup>
  import { computed } from 'vue'

  import {
    Table,
    TableBody,
    TableHead,
    TableHeader,
    TableRow
  } from '@/components/ui/table'
  import ArrayVisualizerCell from './ArrayVisualizerCell.vue'
  import { useCellProps } from './cell'

  const props = defineProps({
    array: {
      type: Array,
      default: () => []
    },
    showIndexes: {
      type: Boolean,
      default: false
    },
    vertical: {
      type: Boolean,
      default: false
    },
    ...useCellProps
  })
  const is2D = computed(() => Array.isArray(props.array[0]))
  const cellProps = props
</script>