import { z } from 'zod'

import { splitTopLevel, trimBraces } from '../utils.js'

export function arraySorted (array, desc = false) {
  for (let i = 1; i < array.length; i++) {
    if (desc ? array[i] > array[i - 1] : array[i] < array[i - 1]) {
      return false
    }
  }
  return true
}

export function arrayUnique (array) {
  return array.length === new Set(array).size
}

export function toArraySchema (type, { toSchema }) {
  let res = null
  if (type.items?.length) {
    const itemType = type.items[0]
    res = z.array(toSchema(itemType))
  } else {
    res = z.array(z.any())
  }
  if (type.attrs?.includes('sorted')) {
    res = res.refine(arraySorted)
  }
  if (type.attrs?.includes('unique')) {
    res = res.refine(arrayUnique)
  }
  return res
}

export function createArray (type, { create }) {
  const itemType = type.items?.length ? type.items[0] : 'any'
  const length = 10
  let res = []
  for (let i = 0; i < length; i++) {
    res.push(create(itemType))
  }
  if (type.attrs?.includes('sorted')) {
    res.sort((a, b) => a == b ? 0 : (a > b ? 1 : -1))
  }
  if (type.attrs?.includes('unique')) {
    res = [...new Set(res)]
  }
  return res
}

// Формат: "[1,2,3]"
export function parseArray (str, type, { parse }) {
  str = trimBraces(str)
  return splitTopLevel(str)
    .map(v => v.trim())
    .map(v => parse(v, type.items?.[0]))
}