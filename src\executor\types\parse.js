import { parseTypeString } from './type-parser.js'
import { parseString, parseChar } from './string/index.js'
import { parseNumber, parseInteger } from './number/index.js'
import { parsePair } from './pair/index.js'
import { parseArray } from './array/index.js'
import { parseBst } from './tree/index.js'

const parsers = {
  string: parseString,
  char: parseChar,
  number: parseNumber,
  integer: parseInteger,
  array: parseArray,
  // graph: parseGraph,
  pair: parsePair,
  bst: parseBst,
  any: v => v,
  boolean: b => ['1','true'].includes(b)
}

export function parse (value, type) {
  if (!type) {
    return value
  }
  type = parseTypeString(type)
  const parser = parsers[type.name]
  if (!parser) {
    throw new Error(`Неизвестный тип ${type.name}`)
  }
  return parser(value, type, { parse })
}