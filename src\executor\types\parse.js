import { parseTypeString } from './type-parser'
import { parseString, parseChar } from './string'
import { parseNumber, parseInteger } from './number'
import { parsePair } from './pair'
import { parseArray } from './array'
import { parseBst } from './tree'

const parsers = {
  string: parseString,
  char: parseChar,
  number: parseNumber,
  integer: parseInteger,
  array: parseArray,
  // graph: parseGraph,
  pair: parsePair,
  bst: parseBst,
  any: v => v,
  boolean: b => ['1','true'].includes(b)
}

export function parse (value, type) {
  if (!type) {
    return value
  }
  type = parseTypeString(type)
  const parser = parsers[type.name]
  if (!parser) {
    throw new Error(`Неизвестный тип ${type.name}`)
  }
  return parser(value, type, { parse })
}