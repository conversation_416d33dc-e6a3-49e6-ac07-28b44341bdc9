import { describe, test, expect } from 'vitest';
import {
  linearProbing,
  quadraticProbing,
  doubleHashing,
  hash
} from './functions.js';

describe('Collision Resolution Functions', () => {
  const TABLE_SIZE = 10;
  const TEST_KEY = "test_key";

  describe('linearProbing()', () => {
    test('should move to next index with step=1', () => {
      expect(linearProbing(5, TABLE_SIZE)).toBe(6);
      expect(linearProbing(9, TABLE_SIZE)).toBe(0);
    });

    test('should handle custom step size', () => {
      expect(linearProbing(5, TABLE_SIZE, 3)).toBe(8);
      expect(linearProbing(8, TABLE_SIZE, 3)).toBe(1); 
    });
  });

  describe('quadraticProbing()', () => {
    test('should calculate correct indices', () => {
      expect(quadraticProbing(5, TABLE_SIZE, 1)).toBe(6); // 5 + 1² = 6
      expect(quadraticProbing(5, TABLE_SIZE, 2)).toBe(9); // 5 + 2² = 9
      expect(quadraticProbing(5, TABLE_SIZE, 3)).toBe(4); // 5 + 3² = 14 → 14%10=4
    });

    test('should wrap around correctly', () => {
      expect(quadraticProbing(8, TABLE_SIZE, 3)).toBe(7); // 8 + 9 = 17 → 7
    });
  });

  describe('hash()', () => {
    test('should produce consistent results', () => {
      const h1 = hash(TEST_KEY, TABLE_SIZE);
      const h2 = hash(TEST_KEY, TABLE_SIZE);
      expect(h1).toBe(h2);
    });

    test('should handle different table sizes', () => {
      const h1 = hash(TEST_KEY, 10);
      const h2 = hash(TEST_KEY, 20);
      expect(h1).not.toBe(h2);
    });

    test('should handle non-string keys', () => {
      expect(() => hash(12345, TABLE_SIZE)).not.toThrow();
      expect(() => hash({ object: 'key' }, TABLE_SIZE)).not.toThrow();
    });

    test('should limit key length to 100 chars', () => {
      const longKey = "a".repeat(150);
      const hash1 = hash(longKey, TABLE_SIZE);
      const hash2 = hash(longKey.substring(0, 100), TABLE_SIZE);
      expect(hash1).toBe(hash2);
    });
  });

  describe('Integration Tests', () => {
    test('full collision resolution flow', () => {
      const key = "important_key";
      const size = 5;
      const initialIndex = hash(key, size);
      
      const collisionIndex = initialIndex;
      
      const linearNext = linearProbing(collisionIndex, size);
      
      const quadraticNext = quadraticProbing(collisionIndex, size, 1);
      
      const doubleNext = doubleHashing(key, collisionIndex, size, 1);
      
      expect(linearNext).not.toBe(collisionIndex);
      expect(quadraticNext).not.toBe(collisionIndex);
      expect(doubleNext).not.toBe(collisionIndex);
    });
  });
});