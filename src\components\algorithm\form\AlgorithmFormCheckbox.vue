<template>
  <FormField
    v-slot="{ value, handleChange }"
    :name="name"
  >
    <FormItem>
      <div class="flex flex-row items-start space-x-3 space-y-0">
        <FormControl>
          <Checkbox
            :model-value="value"
            @update:model-value="handleChange"
          />
        </FormControl>

        <FormLabel>{{ config.label }}</FormLabel>
      </div>

      <FormDescription v-if="config.description">
        {{ config.description }}
      </FormDescription>
    </FormItem>
  </FormField>
</template>

<script setup>
  import { Checkbox } from '@/components/ui/checkbox'
  import {
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel
  } from '@/components/ui/form'

  import { useAlgorithmFormItemProps } from './item'

  const props = defineProps(useAlgorithmFormItemProps)
</script>