import { expect, test, describe } from 'vitest';
import murmurHash3 from './murmurhash3';

describe('MurmurHash3', () => {
  describe('x86.hash32', () => {
    test('empty string returns 0', () => {
      expect(murmurHash3.x86.hash32('', 0)).toBe(0);
    });

    test('hash for "hello" is correct', () => {
      expect(murmurHash3.x86.hash32('hello', 0)).toBe(613153351);
    });

    test('hash for "world" is correct', () => {
      expect(murmurHash3.x86.hash32('world', 0)).toBe(4220927227);
    });
  });

  describe('x86.hash128', () => {
    test('empty string returns zero hash', () => {
      expect(murmurHash3.x86.hash128('', 0)).toBe('00000000000000000000000000000000');
    });

    test('hash for "hello" is correct', () => {
      expect(murmurHash3.x86.hash128('hello', 0)).toBe('2b2444a0db91def79adb31b69adb31b6');
    });
  });

  describe('x64.hash128', () => {
    test('empty string returns zero hash', () => {
      expect(murmurHash3.x64.hash128('', 0)).toBe('00000000000000000000000000000000');
    });

    test('hash for "hello" is correct', () => {
      expect(murmurHash3.x64.hash128('hello', 0)).toBe('cbd8a7b341bd9b025b1e906a48ae1d19');
    });
  });

  describe('Edge cases', () => {
    test('unicode characters', () => {
      expect(murmurHash3.x86.hash32('😊', 0)).toBe(1228155785);
    });

    test('different seeds produce different hashes', () => {
      const hash1 = murmurHash3.x86.hash32('test', 0);
      const hash2 = murmurHash3.x86.hash32('test', 1);
      expect(hash1).not.toBe(hash2);
    });
  });
});