{"name": "platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "release": "npm run test && npm run build && npm run git:merge-main-into-prod", "git:merge-main-into-prod": "git checkout prod && git merge main && git push origin prod && git checkout main"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.3.2", "lucide-vue-next": "^0.503.0", "mermaid": "^11.6.0", "reka-ui": "^2.4.0", "simple-code-editor": "^2.0.9", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "v-network-graph": "^0.9.21", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "zod": "^3.25.7", "zod-i18n-map": "^2.27.0"}, "devDependencies": {"@babel/generator": "^7.27.3", "@babel/parser": "^7.27.3", "@babel/traverse": "^7.27.3", "@iconify-json/radix-icons": "^1.2.2", "@iconify/vue": "^4.3.0", "@types/node": "^22.15.2", "@vitejs/plugin-vue": "^5.2.2", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "happy-dom": "^18.0.1", "vite": "^6.3.1", "vitest": "^3.1.2", "vue-tsc": "^2.2.8"}}