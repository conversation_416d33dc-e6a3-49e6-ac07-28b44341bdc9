import { computed } from 'vue'

export const useCellProps = {
  highlighted: {
    type: Array,
    default: () => []
  },
  highlightStyle: {
    type: Function,
    default: v => ({})
  },
  highlightClass: {
    type: Function,
    default: v => ['bg-white/15']
  },
  cellContent: {
    type: Function,
    default: v => v
  },
  cellStyle: {
    type: Function,
    default: v => ({})
  },
  cellClass: {
    type: Function,
    default: v => []
  }
}

export function useCell (props) {
  const isHighlighted = computed(
    () => Array.isArray(props.pos)
      ? props.highlighted.some(x => x[0] === props.pos[0] && x[1] === props.pos[1])
      : props.highlighted.includes(props.pos)
  )

  return {
    isHighlighted
  }
}
