import { binarySearch } from "../binary-search"

const create = itemType => ({
	key: `exponential-search-${itemType}`,
	label: `Экспоненциальный поиск (${itemType})`,
	description: 'Поиск в отсортированном массиве. Основан на прыжках по массиву с экспоненциальным шагом и конкретизации диапазона поиска.',
	complexity: {
		time: 'O(log n)'
	},
	initial: 'start',
	dependencies: [`binary-search-${itemType}`],
  
	data: {
		array: {
			label: 'Массив',
			description: 'Отсортированный массив, по которому осуществляется поиск',
			type: `array<${itemType}>`,
			input: true,
			required: true
	  },
		needle: {
			label: 'Искомое',
			description: 'Искомое значение',
			type: itemType,
			input: true,
			required: true
		},
    multiplier: {
      label: 'Множитель',
      description: 'Множитель для определения размера прыжков',
      type: 'integer',
      default: 2,
			input: true
    },
		index: {
			label: 'Индекс',
			description: 'Индекс найденного значения или -1 в случае неудачи',
			type: 'integer',
			output: true
		},
		iterations: {
			label: 'Итераций',
			description: 'Количество итераций',
			type: 'integer',
      default: 0,
			output: true
		},
		i: {
			description: 'Индекс текущего элемента',
			type: 'integer'
		},
		j: {
			description: 'Индекс правой границы диапазона',
			type: 'integer'
		}
	},
  
	blocks: {
		start: {
			label: 'i = 1',
			next: 'checkBounds'
		},
		checkBounds: {
			label: 'i < N ?',
			conditions: [
				{
					label: 'Да',
					next: 'checkValue'
				},
				{
					label: 'Нет',
					next: 'finish'
				}
			]
		},
		checkValue: {
			label: 'array[i] < needle ?',
			conditions: [
				{
					label: 'Да',
					next: 'jump'
				},
				{
					label: 'Нет',
					next: 'bs:start'
				}
			]
		},
		jump: {
			label: 'i = i * multiplier',
			next: 'checkBounds'
		},

		...binarySearch(itemType).blocks
	},
  
	invoke ({ array, needle, multiplier = 2 }) {
		let left = -1
		let right = 1
		let iterations = 0
		// msg: 'Инициализируем индекс текущего элемента'
	
		while (right < array.length) {
			iterations += 1
			// msg: `Проверяем элемент ${array[right]} (индекс ${right})`

			if (array[right] === needle) {
				// msg: 'Элемент по индексу совпадает с искомым'

				return { index: right, iterations }
			} else if (array[right] < needle) {
				// msg: 'Элемент меньше искомого, увеличиваем индекс'
	
				left = right
				right *= multiplier
				// msg: `Новый индекс: ${right}`
			} else {
				// msg: 'Элемент больше или равен искомому, переходим к бинарному поиску'
				break
			}
		}
	
		return binarySearch(itemType).invoke({
			array,
			needle,
			left: left + 1,
			right: Math.min(right - 1, array.length - 1)
		})
	}
})

export const exponentialSearchNumber = create('number')
export const exponentialSearchString = create('string')