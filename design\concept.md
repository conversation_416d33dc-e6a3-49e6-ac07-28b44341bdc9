# Концепция проекта

Ориентация на изучение принципов работы алгоритмов и структур данных. Для этого юзеру необходимо:
- Видеть схему алгоритма, чтобы держать общую картину в голове
- Выполнять алгоритм пошагово, чтобы иметь возможность постепенно разбираться
- Видеть и отслеживать состояние данных на каждом шаге, чтобы детально понимать, что происходит
- Иметь возможность возвращаться к предыдущим шагам, чтобы вернуться на путь понимания в случае трудностей или осваивать итеративно
- Видеть дополнительную инфу об алгоритмах, чтобы не лезть в сторонние источники (самодостаточность ресурса)
- Иметь возможность менять входные данные, чтобы понять, как алгоритм работает в разных ситуациях

## Основные экраны

- Главная страница
  - Описание
  - Список контрибьюторов
  - Ссылки / карточки на некоторые экраны
- Страница алгоритма
  - Панель управления (выбор алгоритма, управление ходом выполнения, возврат к предыдущим шагам, управление входными данными)
  - Блок-схема алгоритма
  - Описание, сложность и прочие сведения
  - Визуализация текущего состояния структур данных
  - Указание текущих значений прочих переменных
  - Лог выполнения
- Страница структуры данных
  - То же, что страница алгоритма, но на одной структуре (структурах) может быть последовательно выполнено несколько алгоритмов (вставки, поиски, удаления и т.д.)
- (?) Интерактивная песочница
  - Не посвящена конкретному алгоритму или стуртуре данных
  - Позволяет подготавливать любые произвольные данные и выполнять над ними любые подходящие алгоритмы

## Обязательно добавить

- Поиск по алгоритмам / структурам
- Подробная инфа по алгоритмам и структурам со ссылками на источники, конкретными кейсами применимости и т.д.

## Возможные направления для развития

- Импорт / экспорт состояния
- Электронная версия методички (когда будет готова)
- Галерея избранных проектов (+ ссылки на задеплоенные)
- Автоматическая генерация тестов: генерация случайных ситуаций и просьба ответить, что будет при совершении каких-то действий
- Паттерны проектирования
- Архитектуры распределённых систем
- Архитектуры приложений
- Элементы геймификации
