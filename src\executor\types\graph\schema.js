import { z } from 'zod'

import { instanceSchema } from '../utils.js'
import { Graph } from './graph.js'

const subType = {
  weighted: (schemas, type, toSchema) => {
    let edgeDataSchema = type.items?.length
      ? toSchema(type.items[0])
      : z.any()
    schemas.links = z.array(
      z.object({ data: edgeDataSchema }).passthrough()
    )
  },

  directed: (schemas) => {
    schemas.isDirected = z.literal(true)
  }
}

/**
 * Составление zod-схемы валидации графа
 * @param {*} type - описание структуры данных
 * @param {Function} toSchema - функция превращающая объект в zod-схему
 * @param {Function} parseType - функция нахождения подтипа графа
 * @returns zod-схема валидации графа 
 */
export function toGraphSchema (type, { toSchema, parseType }) {
  const schemas = {}

  if (type.items?.length) {
    const nodeDataType = type.items[0]
    schemas.nodes = z.array(
      z.object({ data: toSchema(nodeDataType) }).passthrough()
    )
  }
  
  if (type.sub?.length) {
    type.sub.forEach(sub => {
      sub = parseType(sub)
      const subConverter = subType[sub.name]
      if (!subConverter) {
        throw new Error(`Неизвестный подтип ${sub.name}`)
      }
      subConverter(schemas, sub, toSchema)
    })
  }
  
  return instanceSchema(Graph, schemas)
}