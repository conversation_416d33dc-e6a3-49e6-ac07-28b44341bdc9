<template>
  <div class="p-10">
    <h1 class="mb-12 scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl">
      Алгоритмы и структуры данных
    </h1>

    <p class="leading-7 mt-6">
      Приложение реализует наглядные визуализаторы основных принципов работы структур данных и алгоритмов. Предназначено для студентов IT-специальностей, а также всех интересующихся.
    </p>

    <p class="leading-7 mt-6">
      Проект изначально создан как учебный материал для курса «Структуры и алгоритмы компьютерной обработки данных», преподаваемого на 2 курсе направления «Прикладная информатика» <a href="https://www.cs.vsu.ru/" target="__blank" class="font-medium text-primary underline underline-offset-4">факультета компьютерных наук</a> Воронежского государственного университета.
    </p>

    <p class="leading-7 mt-6">
      По любым вопросам, предложениям и замечаниям пишите на <a href="mailto:<EMAIL>" target="__blank" class="font-medium text-primary underline underline-offset-4"><Mail :size="18" class="inline mx-1" /><EMAIL></a>.
    </p>

    <h2 class="mt-10 scroll-m-20 border-b pb-2 text-3xl font-extrabold tracking-tight transition-colors first:mt-0">
      Контрибьюторы
    </h2>

    <p class="leading-7 mt-6">
      <b>Коротков В. В.</b> - руководитель проекта, преподаватель курса. A&D, архитектура проекта, движок выполнения алгоритмов, DSL для описания алгоритмов, реализация базовых структур данных, визуализация массивов.
    </p>
  </div>
</template>

<script setup>
  import { Mail } from 'lucide-vue-next'
</script>
