const loopStyles = ['loop1', 'loop2', 'loop3']
const loopColors = ['#3444bb', '#bb3444', '#44bb34']
const loopStyleClasses = `
    classDef loop1 stroke: ${loopColors[0]}, stroke-width: 2px
    classDef loop2 stroke: ${loopColors[1]}, stroke-width: 2px
    classDef loop3 stroke: ${loopColors[2]}, stroke-width: 2px
`

export function blocksToFlowchartMermaid (blocks) {
  if (!blocks) {
    return ''
  }

  let definition = 'flowchart TD\n'
  let loopIndex = 0
  const loopKeyIndex = {}

  // Создаем узлы
  Object.entries(blocks).forEach(([key, block]) => {
    let nodeShape = ''
    let loopStyle = null
    const label = block.label ?? ' '

    if (['loop-start', 'loop-end'].includes(block.type)) {
      if (!(block.loop in loopKeyIndex)) {
        loopKeyIndex[block.loop] = loopIndex++
      }
      loopStyle = loopStyles[loopKeyIndex[block.loop] % loopStyles.length]
    }

    switch (block.type) {
      case 'start':
        nodeShape = `${key}@{ shape: circle, label: "${label}" }`
        break
      case 'finish':
        nodeShape = `${key}@{ shape: dbl-circ, label: "${label}" }`
        break
      case 'condition':
        nodeShape = `${key}{"${label}?"}`
        break
      case 'loop-start':
        nodeShape = `${key}[/"${label}"\\]:::${loopStyle}`
        break
      case 'loop-end':
        nodeShape = `${key}[\\"${label}"/]:::${loopStyle}`
        break
      case 'call':
        nodeShape = `${key}[["${label}"]]`
        break
      default:
        nodeShape = `${key}["${label}"]`
        break
    }

    if (block.description) {
      nodeShape += `\n    click ${key} href "javascript:void(0)" "${block.description}"`
    }
    
    definition += `    ${nodeShape}\n`
  })

  // Создаем связи
  Object.entries(blocks).forEach(([key, block]) => {
    if (block.next) {
      // Для обычных блоков с одним переходом
      definition += `    ${key} --> ${block.next}\n`
    } else if (block.conditions) {
      // Для условных блоков с несколькими переходами
      block.conditions.forEach(condition => {
        definition += `    ${key} -->|${condition.label ?? ''}| ${condition.next}\n`
      })
    }
  })

  definition += loopStyleClasses
  
  return definition
}