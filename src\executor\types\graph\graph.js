import { clone } from '../../utils/clone'

export class Graph {
  _nodes = {}
  _lists = {}
  _config = {}

  constructor (config = {}) {
    this._config = config
  }

  get isDirected () {
    return this._config.directed ?? false
  }

  _nextId () {
    return Math.max(0, ...Object.keys(this._nodes).map(Number)) + 1
  }

  get nodes () {
    return Object.values(this._nodes)
  }

  get links () {
    return Object.values(this._lists).flat()
  }

  getNode (id) {
    return this._nodes[id] ?? null
  }

  getLinks (node) {
    return this._lists[node.id] ?? []
  }

  getLinkedNodes (node) {
    return this.getLinks(node).map(link => this.getNode(link.id))
  }

  getLink (node1, node2) {
    return this._lists[node1.id]?.find(link => link.id === node2.id) ?? null
  }

  isLinked (node1, node2) {
    return !!this.getLink(node1, node2)
  }

  getReversedLink (link) {
    return this.getLink(this.getNode(link.id), this.getNode(link.id2))
  }

  addNode (data) {
    const id = this._nextId()
    const n = { data, id }
    this._nodes[id] = n
    return n
  }

  removeNode (node) {
    delete this._nodes[node.id]
    delete this._lists[node.id]
  }

  addLink (node1, node2, data) {
    const id1 = node1.id
    const id2 = node2.id
    if (id1 === id2) {
      throw Error(`Невозможно создать петлю для узла ${id1}`)
    }
    if (this.isLinked(node1, node2)) {
      throw Error(`Связь между узлами ${id1} и ${id2} уже существует`)
    }
    if (!this._nodes[id1] || !this._nodes[id2]) {
      throw Error(`Узел ${id1} или ${id2} не существует`)
    }
    if (!this._lists[id1]) {
      this._lists[id1] = []
    }
    this._lists[id1].push({ id: id2, data, id2: id1 })
    if (!this.isDirected) {
      if (!this._lists[id2]) {
        this._lists[id2] = []
      }
      this._lists[id2].push({ id: id1, data: clone(data), id2 })
    }
  }
  
  removeLink (node1, node2) {
    const id1 = node1.id
    const id2 = node2.id
    if (!this._lists[id1] || !this._lists[id2] || !this.isLinked(node1, node2)) {
      return
    }
    this._lists[id1] = this._lists[id1]?.filter(link => link.id !== id2)
    if (!this.isDirected) {
      this._lists[id2] = this._lists[id2]?.filter(link => link.id !== id1)
    }
  }

  clear () {
    this._nodes = {}
    this._lists = {}
  }

  clone () {
    const res = new Graph(this._config)
    res._nodes = clone(this._nodes)
    res._lists = clone(this._lists)
    return res
  }
}