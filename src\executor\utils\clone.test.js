import { cloneContext } from "./clone"

describe('cloneContext', () => {
  it('should clone context', () => {
    const ctx = {
      a: 1,
      array: [1,2,3],
      obj: {
        d: 4
      }
    }
    const cloned = cloneContext(ctx)
    expect(cloned).toEqual(ctx)
    expect(cloned == ctx).toBe(false)
    expect(cloned.array == ctx.array).toBe(false)
    expect(cloned.obj == ctx.obj).toBe(false)
    expect(ctx.a).toEqual(cloned.a)
    expect(ctx.array).toEqual(cloned.array)
    expect(ctx.obj).toEqual(cloned.obj)
  })
})
