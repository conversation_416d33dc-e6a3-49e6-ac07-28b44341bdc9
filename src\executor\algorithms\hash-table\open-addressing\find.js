export const hashTableOpenAddressingFind = (keyType, valueType) => ({
    key: "hash-table-open-addressing-find",
    label: "Поиск в HashTable Open Addressing",
    description: "Поиск значения в хеш-таблице с открытой адресацией",
    complexity: {
        time: {
            worst: "O(n)",
            avg: "O(1)",
            best: "O(1)"
        }
    },
    initial: "start",

    data: {
        hashTable: {
            label: "HashTableOpenAddressing",
            description: "Хеш-таблица с открытой адресацией",
            type: "hash-table-open-addressing",
            input: true,
            required: true
        },
        key: {
            label: "Ключ",
            description: "Ключ для поиска значения",
            type: "any",
            input: true,
            required: true
        },
        value: {
            label: "Найденное значение",
            description: "Результат поиска",
            type: "any",
            output: true
        },
        index: {
            label: "Текущий индекс",
            description: "Индекс при поиске в таблице",
            type: "integer"
        }
    },

    blocks: {
        start: {
            type: "start",
            label: "Начало поиска",
            description: "Инициализация процесса поиска элемента",
            next: "calculateIndex"
        },

        calculateIndex: {
            label: "Вычисление индекса",
            description: "Получаем текущий индекс для проверки",
            next: "loopStart"
        },
        
        loopStart: {
            type: "loop-start",
            label: "Начало цикла поиска",
            description: "Цикл проверки слотов таблицы",
            loop: "search_loop",
            next: "checkSlot"
        },
        
        checkSlot: {
            type: "condition",
            label: "Проверка слота",
            description: "Проверяем текущий слот таблицы",
            conditions: [
                {
                    label: "Слот пуст",
                    description: "Элемент не найден",
                    next: "loopEnd"
                },
                {
                    label: "Слот занят",
                    description: "Проверяем ключ элемента",
                    next: "compareKey"
                }
            ]
        },
        
        compareKey: {
            type: "condition",
            label: "Сравнение ключей",
            description: "Проверяем совпадение ключей",
            conditions: [
                {
                    label: "Ключи совпадают",
                    description: "Элемент найден",
                    next: "returnValue"
                },
                {
                    label: "Ключи разные",
                    description: "Продолжаем поиск",
                    next: "nextSlot"
                }
            ]
        },
        
        nextSlot: {
            label: "Следующий слот",
            description: "Вычисляем индекс следующего слота",
            next: "loopStart"
        },
        
        loopEnd: {
            type: "loop-end",
            label: "Конец цикла поиска",
            description: "Завершение цикла проверки слотов",
            loop: "search_loop",
            next: "returnNull"
        },
        
        returnValue: {
            label: "Возврат значения",
            description: "Возвращаем найденное значение",
            next: "finish"
        },
        
        returnNull: {
            label: "Элемент не найден",
            description: "Возвращаем null",
            next: "finish"
        },
        
        finish: {
            type: "finish",
            label: "Поиск завершен",
            description: "Операция поиска завершена"
        }
    },

    invoke: ({ hashTable, key }) => {
        // block: 'calculateIndex'
        // msg: Вычислем индекс элемента через хеш ключа элемента
        let index = hashTable.hash(key)
        
        // block: 'loopStart'

        // block: 'checkSlot'
        // msg: Проверяем пустой ли слот
        while (hashTable._table[index] !== null) {
            // block: 'compareKey'
            // msg: Сравниваем текущий и искомые ключи
            if (hashTable._table[index][0] === key) {
                // block: 'returnValue'
                // msg: Нашли значение и возвращаем его
                return hashTable._table[index][1]
            }
            // block: 'nextSlot'
            // msg: Вычислем следующий слот при помощи линейного пробирования
            index = (index + 1) % hashTable._size
        }
        // block: 'loopEnd'

        // block: 'returnNull'
        // msg: Не нашли элемент в таблице и возвращаем null
        return null
    }
});

export const hashTableOpenAddressingFindAnyAny = hashTableOpenAddressingFind('any', 'any')