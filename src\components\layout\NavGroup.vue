<template>
  <SidebarGroup>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in items">
        <SidebarMenuButton :tooltip="item.title" asChild>
          <router-link :to="item.to">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>

<script setup lang="ts">
  import type { LucideIcon } from 'lucide-vue-next'
  import {
    SidebarGroup,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem
  } from '@/components/ui/sidebar'

  defineProps<{
    items: {
      title: string
      to: string | object
      icon?: LucideIcon
      isActive?: boolean
    }[]
  }>()
</script>
