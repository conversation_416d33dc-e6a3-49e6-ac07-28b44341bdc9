import { expect, test } from 'vitest'
import { Graph } from './graph'

test('graph to work properly', () => {
  const g = new Graph()
  const n1 = g.addNode(1)
  const n2 = g.addNode(2)
  const n3 = g.addNode(3)
  const n4 = g.addNode(4)
  const n5 = g.addNode(5)

  g.addLink(n1, n2)
  expect(() => g.addLink(n2, n1)).toThrow()
  g.addLink(n1, n3)
  expect(() => g.addLink(n3, n1)).toThrow()
  g.addLink(n2, n4)
  g.addLink(n3, n4)
  g.addLink(n4, n5)

  expect(g.nodes.length).toBe(5)
  expect(g.getLinks(n1).length).toBe(2)
  expect(g.getLinks(n2).length).toBe(2)
  expect(g.getLinks(n3).length).toBe(2)
  expect(g.getLinks(n4).length).toBe(3)
  expect(g.getLinks(n5).length).toBe(1)

  expect(g.isLinked(n1, n2)).toBe(true)
  expect(g.isLinked(n2, n1)).toBe(true)
  expect(g.isLinked(n1, n3)).toBe(true)
  expect(g.isLinked(n3, n1)).toBe(true)
  expect(g.isLinked(n2, n4)).toBe(true)
  expect(g.isLinked(n4, n2)).toBe(true)
  expect(g.isLinked(n3, n4)).toBe(true)
  expect(g.isLinked(n4, n3)).toBe(true)
  expect(g.isLinked(n4, n5)).toBe(true)
  expect(g.isLinked(n5, n4)).toBe(true)

  g.removeLink(n1, n2)
  expect(g.isLinked(n1, n2)).toBe(false)
  expect(g.isLinked(n2, n1)).toBe(false)

  g.removeLink(n1, n3)
  expect(g.isLinked(n1, n3)).toBe(false)
  expect(g.isLinked(n3, n1)).toBe(false)

  g.removeLink(n2, n4)
  expect(g.isLinked(n2, n4)).toBe(false)
  expect(g.isLinked(n4, n2)).toBe(false)

  g.removeLink(n3, n4)
  expect(g.isLinked(n3, n4)).toBe(false)
  expect(g.isLinked(n4, n3)).toBe(false)

  g.removeLink(n4, n5)
  expect(g.isLinked(n4, n5)).toBe(false)
  expect(g.isLinked(n5, n4)).toBe(false)

  expect(g.nodes.length).toBe(5)
  expect(g.getLinks(n1).length).toBe(0)
  expect(g.getLinks(n2).length).toBe(0)
  expect(g.getLinks(n3).length).toBe(0)
  expect(g.getLinks(n4).length).toBe(0)
  expect(g.getLinks(n5).length).toBe(0)

  g.clear()
  expect(g.nodes.length).toBe(0)
})
