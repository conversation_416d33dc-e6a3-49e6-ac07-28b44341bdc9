import { hasSubtype } from "../utils.js"
import { HashTableChained, HashTableOpenAddressing } from "./hash.js"

/**
 * Создаем хеш-таблицу и заполняем её случайными значениями
 * @param {Object} type - тип данных для ключа и значения  
 * {
 *  items: {
 *      key: any, 
 *      value: any
 *  },
 *  subtypes: {
 *      "addressing"
 *  }
 * }
 * @param {Function} create - функция генерации значений
 * @param {Function} parseType - функция находжения подтипа хеш-таблицы
 */
export function createHashTable( type, { create, parseType }) {
    const keyType = type.items?.key ? type.items.key : 'any'
    const valueType = type.items?.value ? type.items.value : 'any'

    const chained = hasSubtype(type, 'chained', parseType)
    const openAddressing = hasSubtype(type, 'addressing', parseType)

    const size = 10
    let hashTable;

    if (chained) {
        hashTable = createHashTableChained(size, create, keyType, valueType)
    }
    if (openAddressing) {
        hashTable = createHashTableOpenAddressing(size, create, keyType, valueType)
    }

    return hashTable
}

function createHashTableOpenAddressing(size, create, keyType, valueType) {
    let hashTable = new HashTableOpenAddressing({ size })

    for (let i = 0; i < size; i++) {
        const key = create(keyType)
        const value = create(valueType)
        hashTable.set(key, value)
    }

    return hashTable
}

function createHashTableChained(size, create, keyType, valueType) {
    let hashTable = new HashTableChained({ size })

    for (let i = 0; i < size; i++) {
        const key = create(keyType)
        const value = create(valueType)
        hashTable.set(key, value)
    }

    return hashTable
}