const create = itemType => ({
	key: `insertion-sort-${itemType}`,
	label: `Сортировка вставками (${itemType})`,
	description: 'Реализация по месту на массиве. Сортировка основана на переборе элементов неотсортированной части массива и их вставке на нужную позицию в отсортированную часть.',
	complexity: {
		time: {
			worst: 'O(n²)',
			avg: 'O(n²)',
			best: 'O(n)'
		},
		memory: 'O(1)'
	},
	sources: [
		{
			label: 'Википедия',
			url: 'https://ru.wikipedia.org/wiki/Сортировка_вставками'
		},
		{
			label: 'Вики-конспекты',
			url: 'https://neerc.ifmo.ru/wiki/index.php?title=Сортировка_вставками'
		},
		{
			label: 'Статья на Хабре',
			url: 'https://habr.com/ru/articles/415935/'
		}
	],

	data: {
		array: {
			label: 'Массив',
			description: 'Массив для сортировки',
			type: `array<${itemType}>`,
			input: true,
			required: true,
			output: true
	  },
		desc: {
			label: 'По убыванию',
			default: false,
			type: 'boolean',
			input: true
		},
		swaps: {
			label: 'Обменов',
			description: 'Количество обменов',
			type: 'integer',
      default: 0,
			output: true
		},
		comparisons: {
			label: 'Сравнений',
			description: 'Количество сравнений',
			type: 'integer',
      default: 0,
			output: true
		},
		i: {
			description: 'Индекс текущего элемента',
			type: 'integer'
		},
		j: {
			description: 'Индекс позиции для вставки',
			type: 'integer'
		},
		value: {
			description: 'Текущий элемент',
			type: itemType
		}
	},

	blocks: {
		start: {
			type: 'start',
			next: 'outerLoopStart'
		},
		outerLoopStart: {
			type: 'loop-start',
			loop: 'outer',
			label: 'Цикл по i от 1 до N-1',
			next: 'saveKey'
		},
		saveKey: {
			label: 'value = array[i]\nj = i',
			next: 'innerLoopStart'
		},
		innerLoopStart: {
			type: 'loop-start',
			loop: 'inner',
			label: 'Пока j > 0 и value < array[j-1]',
			next: 'shiftElement'
		},
		shiftElement: {
			label: 'array[j] = array[j-1]',
			next: 'innerLoopEnd'
		},
		innerLoopEnd: {
			type: 'loop-end',
			loop: 'inner',
			label: 'Уменьшить j на 1',
			next: 'insertElement'
		},
		insertElement: {
			label: 'array[j] = value',
			next: 'outerLoopEnd'
		},
		outerLoopEnd: {
			type: 'loop-end',
			loop: 'outer',
			label: 'Увеличить i на 1',
			next: 'finish'
		},
		finish: {
			type: 'finish'
		}
	},

	invoke ({ array, desc }) {
		let comparisons = 0
		let swaps = 0
		const cmp = desc
			? (a, b) => a > b
			: (a, b) => a < b

		for (let i = 1; i < array.length; i++) {
			// block: 'outerLoopStart'
			// msg: `Итерация ${i}`
			let value = array[i]
			let j = i
			// block: 'saveKey'
			// msg: `Берём элемент ${value} (индекс ${i}) и ищем его место в отсортированной части`
			comparisons += 1
			// block: 'innerLoopStart'
			// msg: `Сравниваем с элементом ${array[j - 1]} (индекс ${j - 1})`
			while (j > 0 && cmp(value, array[j - 1])) {
				comparisons++

				array[j] = array[j - 1]
				swaps++
				// block: 'shiftElement'
				// msg: `Сдвигаем элемент вправо, j = ${j - 1}`
				j--
				// block: 'innerLoopEnd'
				// msg: `Повторяем, пока не найдём место для вставки`
			}
			// block: 'insertElement'
			// msg: `Нашли правильную позицию (${j}) для элемента ${value}`
			array[j] = value
			// block: 'outerLoopEnd'
			// msg: `Вставили и переходим к следующему элементу`
		}
		// block: 'finish'
		// msg: `Все элементы отсортированы`

		return { array, comparisons, swaps }
	}
})

export const insertionSortNumber = create('number')
export const insertionSortString = create('string')
