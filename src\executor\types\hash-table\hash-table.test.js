import { describe, test, expect, beforeEach } from 'vitest';
import { HashTableOpenAddressing, HashTableChained } from './hash';

describe('HashTableOpenAddressing', () => {
  let table;

  beforeEach(() => {
    table = new HashTableOpenAddressing({ size: 5 });
  });

  test('should initialize with correct size', () => {
    expect(table._size).toBe(5);
    expect(table._table.length).toBe(5);
    expect(table._table.every(slot => slot === null)).toBe(true);
  });

  test('should set and get values', () => {
    table.set('apple', 42);
    table.set('banana', 100);

    expect(table.get('apple')).toBe(42);
    expect(table.get('banana')).toBe(100);
    expect(table.get('orange')).toBeNull();
  });

  test('should handle collisions with linear probing', () => {
    const mockHash = () => 1;
    table.hash = mockHash;

    table.set('a', 1);
    table.set('b', 2); 

    expect(table._table[1]).toEqual(['a', 1]);
    expect(table._table[2]).toEqual(['b', 2]);
  });

  test('should resize when load factor exceeded', () => {
    
    table.set('a', 1);
    table.set('b', 2);
    table.set('c', 3); 
    table.set('d', 4);


    expect(table._size).toBe(10);
    expect(table.get('a')).toBe(1);
    expect(table.get('d')).toBe(4);
  });
});

describe('HashTableChained', () => {
  let table;

  beforeEach(() => {
    table = new HashTableChained({ size: 3 });
  });

  test('should initialize with empty chains', () => {
    expect(table._size).toBe(3);
    expect(table._table.length).toBe(3);
    expect(table._table.every(chain => Array.isArray(chain) && chain.length === 0)).toBe(true);
  });

  test('should set and get values', () => {
    table.set('apple', 42);
    table.set('banana', 100);

    expect(table.get('apple')).toBe(42);
    expect(table.get('banana')).toBe(100);
    expect(table.get('orange')).toBeUndefined();
  });

  test('should handle collisions with chaining', () => {
    const mockHash = () => 1;
    table.hash = mockHash;

    table.set('a', 1);
    table.set('b', 2); 

    expect(table._table[1]).toEqual([
      { key: 'a', value: 1 },
      { key: 'b', value: 2 }
    ]);
  });

  test('should resize when load factor exceeded', () => {
    table.set('a', 1);
    table.set('b', 2);
    table.set('c', 3);

    expect(table._size).toBe(6);
    expect(table.get('a')).toBe(1);
    expect(table.get('c')).toBe(3);
  });

  test('should handle multiple operations', () => {
    const testData = [
      ['apple', 42],
      ['banana', 100],
      ['orange', 200],
      ['grape', 300]
    ];

    testData.forEach(([key, value]) => {
      table.set(key, value);
    });

    testData.forEach(([key, value]) => {
      expect(table.get(key)).toBe(value);
    });

    expect(table._size).toBeGreaterThan(3);
  });
});